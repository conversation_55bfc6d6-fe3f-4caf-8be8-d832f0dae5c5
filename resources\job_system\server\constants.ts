export const GEMMA_CIVILIAN_JOB_PROMPT = `You are designing a new civilian job for a FiveM server. Do not write code. Return ONE JSON object only.

If the input includes parameters (e.g., job_name, police_required=true|false, min_level, economy_multiplier), incorporate them. Otherwise, choose sensible defaults and a job_name fitting Los Santos.

Constraints:
- Output valid JSON only, no comments or extra text.
- Keep the total under 600 words.
- Use USD for money.
- Provide at least 3 distinct locations with example Los Santos vector3 coordinates formatted as {"x": number, "y": number, "z": number}.
- No scripts or code snippets.

JSON fields to produce:
{
  "id": "string-snake_case",
  "name": "string",
  "category": "civilian",
  "summary": "1-2 sentence overview of the job loop",
  "narrative_flavor": "short RP hook",
  "prerequisites": {
    "level": "number or null",
    "licenses": ["list of strings"],
    "items_required": ["list of strings"]
  },
  "start": {
    "how_to_start": "interaction description (e.g., talk to NPC, use target, enter zone)",
    "start_locations": [
      {"label": "string", "coords": {"x": 0, "y": 0, "z": 0}, "radius": 20.0}
    ]
  },
  "flow": {
    "steps": [
      {"order": 1, "label": "string", "description": "what the player does", "success_criteria": "how to complete"}
    ],
    "average_duration_min": 8
  },
  "mechanics": {
    "interactions": ["markers", "targets", "progressbars", "minigame if any"],
    "items": {"consumed": ["list"], "produced": ["list"]},
    "timers": {"per_step_sec": 0, "overall_limit_min": 0},
    "random_events": [{"event": "string", "chance": 0.15, "effect": "string"}],
    "police_alerts": {"enabled": false, "conditions": "when alerts trigger"},
    "cooldowns": {"per_player_min": 5},
    "failure_conditions": ["examples of failure"]
  },
  "locations": [
    {"label": "Pickup", "coords": {"x": 0, "y": 0, "z": 0}, "radius": 15.0},
    {"label": "Route Stop", "coords": {"x": 0, "y": 0, "z": 0}, "radius": 15.0},
    {"label": "Dropoff", "coords": {"x": 0, "y": 0, "z": 0}, "radius": 15.0}
  ],
  "npcs": [
    {"label": "string", "model": "a_m_m_business_01", "role": "giver|receiver", "behavior": "idle|scenario"}
  ],
  "props_vehicles": {
    "vehicles": [{"model": "speedo", "required": false, "spawn_rules": "where/how"}],
    "props": [{"model": "prop_boxpile_06b", "usage": "pickup/dropoff"}]
  },
  "rewards": {
    "base_pay": 400,
    "per_task_bonus": 50,
    "streak_multiplier_max": 1.5,
    "penalties": [{"reason": "damage", "amount": 50}]
  },
  "balance": {
    "difficulty": 2,
    "risk_vs_reward": "low|medium|high",
    "expected_profit_min": 450,
    "expected_profit_max": 800
  },
  "configuration": {
    "economy_multiplier": 1.0,
    "police_required": false,
    "framework": "standalone"
  },
  "events": {
    "client": ["job:start", "job:stepComplete", "job:finish"],
    "server": ["job:assign", "job:reward", "job:fail"]
  },
  "dependencies": {
    "optional": ["ox_lib", "ox_target", "qb-core or es_extended"],
    "notes": "how it can integrate but still be standalone"
  },
  "ui_text": {
    "notifications": ["strings the UI will show"],
    "helptext": ["brief on-screen hints"],
    "progress_labels": ["string"]
  },
  "localization_keys": ["job.name", "job.start", "job.complete"],
  "anti_exploit": ["server-side reward validation", "cooldown checks"],
  "test_scenarios": ["leave vehicle mid-job", "disconnect and reconnect", "inventory full"],
  "extensibility": ["variants, e.g., night shift, VIP delivery"],
  "version": "1.0.0"
}

Return only this JSON object.`;
