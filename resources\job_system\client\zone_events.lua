RegisterNetEvent('ollama_ped:job_ped_zone')
AddEventHandler('ollama_ped:job_ped_zone', function(ped, job)
  local job_label = job.start.start_locations[1].label .. " - Start Job"
  -- SELECT A RANDOM TASK FROM THE AVAILABLE TASKS ARRAY
  exports.ox_target:addSphereZone({
    coords = vec3(job.start.start_locations[1].coords.x, job.start.start_locations[1].coords.y, job.start.start_locations[1].coords.z),
    radius = 1.5,
    debug = true,
    drawSprite = true,
    options = {
        {
            name = 'sphere',
            event = 'ox_target:debug',
            icon = 'fa-solid fa-circle',
            label = job_label,
        }
    }
})
end)

local isPlaying = false

RegisterNetEvent('ollama_ped:phone_call_animation')
AddEventHandler('ollama_ped:phone_call_animation', function(ped)
  if not isPlaying then
    isPlaying = true
    PlayPhoneCallAnimation()
  end

  if isPlaying then
    isPlaying = false
    ClearPedTasks(ped)
  end
end)


-- client.lua
-- Function to play phone call animation (using cellphone anim as proxy for phone booth)
function PlayPhoneCallAnimation()
  local playerPed = PlayerPedId() -- Get local player ped

  -- Load animation dictionary
  local dict = 'cellphone@'
  RequestAnimDict(dict)

  -- Wait for dict to load
  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(500)
  end

  -- Play the animation
  TaskPlayAnim(
    playerPed,
    dict,
    'cellphone_call_listen_base', -- Common phone call anim; adjust if needed
    8.0,                          -- blendInSpeed
    -8.0,                         -- blendOutSpeed
    -1,                           -- duration (-1 for loop)
    49,                           -- flag (loop + upper body only)
    0.0,                          -- playbackRate
    false,
    false,
    false
  )
end
