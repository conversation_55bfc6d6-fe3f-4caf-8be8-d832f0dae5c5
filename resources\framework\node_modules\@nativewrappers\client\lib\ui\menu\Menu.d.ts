import { MenuControls, MenuSettings } from '../';
import { Font, MenuAlignment } from '../../enums';
import { Color, LiteEvent, Point, Size } from '../../utils';
import { UIMenuItem } from './items';
export declare class Menu {
    static screenAspectRatio: number;
    static screenHeight: number;
    static screenWidth: number;
    static screenResolution: Size;
    readonly id: string;
    visible: boolean;
    parentMenu: Menu | undefined;
    parentItem: UIMenuItem | undefined;
    items: UIMenuItem[];
    children: Map<string, Menu>;
    readonly menuOpen: LiteEvent;
    readonly menuClose: LiteEvent;
    readonly menuChange: LiteEvent;
    readonly indexChange: LiteEvent;
    readonly listChange: LiteEvent;
    readonly sliderChange: LiteEvent;
    readonly checkboxChange: LiteEvent;
    readonly listSelect: LiteEvent;
    readonly sliderSelect: LiteEvent;
    readonly itemSelect: LiteEvent;
    readonly panelActivated: LiteEvent;
    private _counterPretext;
    private _counterOverride;
    private _alignment;
    private _offset;
    private _navigationDelay;
    private _lastUpDownNavigation;
    private _lastLeftRightNavigation;
    private _activeItem;
    private _widthOffset;
    private _drawOffset;
    private _justOpened;
    private _mousePressed;
    private _minItem;
    private _maxItem;
    private _maxItemsOnScreen;
    private _controls;
    private _settings;
    private readonly _title;
    private readonly _subtitle;
    private readonly _mainMenu;
    private readonly _logo;
    private readonly _upAndDownSprite;
    private readonly _subtitleResRectangle;
    private readonly _extraRectangleUp;
    private readonly _extraRectangleDown;
    private readonly _descriptionBar;
    private readonly _descriptionRectangle;
    private readonly _descriptionText;
    private readonly _counterText;
    private readonly _background;
    constructor(title: string, subtitle: string, offset?: Point, spriteLibrary?: string, spriteName?: string);
    set Title(text: string);
    get Title(): string;
    get TitleFont(): Font;
    set TitleFont(font: Font);
    set Subtitle(text: string);
    get Subtitle(): string;
    set SubtitleFont(font: Font);
    get SubtitleFont(): Font;
    set SubtitleForeColor(color: Color);
    get SubtitleForeColor(): Color;
    set SubtitleBackColor(color: Color);
    get SubtitleBackColor(): Color;
    get CurrentItem(): UIMenuItem;
    set CurrentItem(value: UIMenuItem);
    get CurrentSelection(): number;
    set CurrentSelection(v: number);
    get Alignment(): MenuAlignment;
    set Alignment(alignment: MenuAlignment);
    get WidthOffset(): number;
    set WidthOffset(widthOffset: number);
    get DrawOffset(): Point;
    get Controls(): MenuControls;
    get Settings(): MenuSettings;
    addNewSubMenu(text: string, description?: string, inherit?: boolean): Menu;
    addSubMenu(subMenuToAdd: Menu, text: string, description?: string, inherit?: boolean): Menu;
    addItem(items: UIMenuItem | UIMenuItem[]): void;
    removeItem(itemOrIndex: UIMenuItem | number): void;
    bindMenuToItem(menuToBind: Menu, itemToBindTo: UIMenuItem): void;
    releaseMenuFromItem(releaseFrom: UIMenuItem): boolean;
    refreshIndex(): void;
    clear(): void;
    open(): void;
    close(): void;
    goLeft(): void;
    goRight(): void;
    selectItem(): void;
    isMouseInBounds(pos: Point, size: Size, drawOffset?: boolean): boolean;
    goUp(): void;
    goDown(): void;
    goBack(): void;
    private _processMouse;
    private _processControl;
    private _isThereAnyItemExcludingSeparators;
    private _playSoundAndReleaseId;
    private _disEnableControls;
    private _recalculateUpAndDown;
    private _recalculateDescriptionPosition;
    private _calculateItemHeight;
    private _calculatePanelPosition;
    private _render;
}
