import { DELAY } from "functions";
import { forceRespawn, setAutoSpawn } from "spawn";

// @ts-ignore next line
const exp = (global as any).exports;
let currentCharacter: any = null;

const DEFAULT_SPAWN_LOCATION = { x: -1612.473, y: 761.133, z: 189.242, heading: 328.646 };
onNet('framework:user_ready', () => {
    const ped = PlayerPedId();
    SetEntityVisible(ped, false, false);
    ShutdownLoadingScreen();
    emit('framework:toggleTargeting', true); // DISABLES
    setAutoSpawn(false);
    SetNuiFocus(false, false); // Keep focus on game until UI is shown
    const [foundGround, groundZ] = GetGroundZFor_3dCoord(DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, false);
    if (foundGround) {
        DEFAULT_SPAWN_LOCATION.z = groundZ;
    }
    SetEntityCoords(ped, DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, true, false, false, true);
    SetEntityHeading(ped, DEFAULT_SPAWN_LOCATION.heading);
});

onNet('framework:showCharacterSelection', (characters: any) => {
    SetNuiFocus(true, true);
    SendNuiMessage(JSON.stringify({
        action: "showCharacterSelection",
        characters: Array.isArray(characters) ? characters : []
    }));
});

// This event will be triggered from the server after character selection/creation is complete.
onNet('framework:spawnPlayer', () => {
    console.log("Spawning player as requested by server.");
    // Re-enable auto-spawn for future respawns (e.g., after death).
    setAutoSpawn(true);
    // Force the player to spawn.
    forceRespawn();
});

// The UI will call this when it's ready
RegisterNuiCallback('uiReady', (data: any, cb: (res: any) => void) => {
    // The UI is ready, now we can request the character data from the server.
    emitNet('framework:server_gather');
    cb({ ok: true });
});

RegisterNuiCallback('selectCharacter', async (character: any, cb: (res: any) => void) => {
    // The UI sent us the character data that was selected
    currentCharacter = character;
    emitNet('framework:characterSelected', character);

    // Hide the NUI and release focus
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: 'hideCharacterSelection' }));

    // Use the model from character data. Fallback to gender-based freemode ped if model is not set.
    const model = character.appearance.model || (character.gender === 'male' ? 'mp_m_freemode_01' : 'mp_f_freemode_01');
    await setPlayerModel(model);
    const ped = PlayerPedId(); // Ped ID can change after setting model

    // Check if the character is new (no appearance components saved)
    if (!character.appearance || !character.appearance.components || character.appearance.components.length === 0) {
        // This is a new character, open the customization menu
        exp["fivem-appearance"].startPlayerCustomization((appearance: any) => {
            if (appearance) {
                console.log("Customization saved");
                emitNet("framework:saveAppearanceData", character.id, JSON.stringify(appearance));
            } else {
                console.log("Customization canceled, returning to selection.");
                emitNet('framework:server_gather'); // Re-fetch characters to go back
            }
        });
    } else {
        // This is an existing character, apply their saved appearance
        exp["fivem-appearance"].setPlayerAppearance(character.appearance);

        // Position the player in the world
        const [foundGround, groundZ] = GetGroundZFor_3dCoord(character.position.x, character.position.y, character.position.z, false);
        if (foundGround) {
            character.position.z = groundZ;
        }
        SetEntityCoords(ped, character.position.x, character.position.y, character.position.z, true, false, false, true);
        SetEntityHeading(ped, character.position.heading);
    }

    ClearPedTasksImmediately(ped);
    RemoveAllPedWeapons(ped, true);
    ClearPlayerWantedLevel(PlayerId());
    FreezeEntityPosition(ped, false);
    SetEntityVisible(ped, true, false);
    SetEntityCollision(ped, true, true);
    emit('framework:toggleTargeting', false); // ENABLES
    cb({ ok: true }); // Acknowledge the callback
});

async function setPlayerModel(model: string | number) {
    if (!model) return;

    if (!IsModelInCdimage(model)) return;

    RequestModel(model);

    while (!HasModelLoaded(model)) {
        await DELAY(0);
    }

    SetPlayerModel(PlayerId(), model);
    SetModelAsNoLongerNeeded(model);

    const playerPed = PlayerPedId();

    if (model === 'mp_m_freemode_01' || model === 'mp_f_freemode_01') {
        SetPedDefaultComponentVariation(playerPed);
        SetPedHeadBlendData(playerPed, 0, 0, 0, 0, 0, 0, 0, 0, 0, false);
    }
}

RegisterNuiCallback('submitNewCharacter', (data: any, cb: (res: any) => void) => {
    emitNet('framework:createCharacter', data);

    // Hide the UI to give feedback that something is happening.
    // It will be shown again by the 'framework:showCharacterSelection' event from the server.
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: 'hideCharacterSelection' }));

    cb({ ok: true });
});

RegisterNuiCallback('deleteCharacter', (data: any, cb: (res: any) => void) => {
    console.log('Requesting to delete character:', data);
    emitNet('framework:server_deleteCharacter', data);

    // Hide the UI to give feedback that something is happening.
    // It will be shown again by the 'framework:showCharacterSelection' event from the server.
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: 'hideCharacterSelection' }));

    cb({ ok: true });
});


RegisterNuiCallback('createCharacter', (data: any, cb: (res: any) => void) => {

    SendNuiMessage(JSON.stringify({ action: 'showCreateForm' }));
    cb({ ok: true });
});

exports('getCurrentCharacter', () => {
    return currentCharacter;
});
