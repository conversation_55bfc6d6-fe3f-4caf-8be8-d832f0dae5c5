import { DELAY } from "functions";
import { forceRespawn, setAutoSpawn } from "spawn";

const DEFAULT_SPAWN_LOCATION = { x: -1612.473, y: 761.133, z: 189.242, heading: 293.613 };
onNet('framework:user_ready', () => {
    ShutdownLoadingScreen();
    emit('framework:toggleTargeting', true); // DISABLES
    setAutoSpawn(false);
    SetNuiFocus(false, false); // Keep focus on game until UI is shown
    const [foundGround, groundZ] = GetGroundZFor_3dCoord(DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, false);
    if (foundGround) {
        DEFAULT_SPAWN_LOCATION.z = groundZ;
    }
    SetEntityCoords(PlayerPedId(), DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, true, false, false, true);
    SetEntityHeading(PlayerPedId(), DEFAULT_SPAWN_LOCATION.heading);
    SetEntityVisible(PlayerId(), false, false);
});

onNet('framework:showCharacterSelection', (characters: any) => {
    SetNuiFocus(true, true);
    SendNuiMessage(JSON.stringify({
        action: "showCharacterSelection",
        characters: Array.isArray(characters) ? characters : []
    }));
});

// This event will be triggered from the server after character selection/creation is complete.
onNet('framework:spawnPlayer', () => {
    console.log("Spawning player as requested by server.");
    // Re-enable auto-spawn for future respawns (e.g., after death).
    setAutoSpawn(true);
    // Force the player to spawn.
    forceRespawn();
});

// The UI will call this when it's ready
RegisterNuiCallback('uiReady', (data: any, cb: (res: any) => void) => {
    // The UI is ready, now we can request the character data from the server.
    emitNet('framework:server_gather');
    cb({ ok: true });
});

RegisterNuiCallback('selectCharacter', async (data: any, cb: (res: any) => void) => {
    // The UI sent us the character data that was selected
    console.log('Character selected:', data);
    emitNet('framework:characterSelected', data);

    // Hide the NUI and release focus
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: 'hideCharacterSelection' }));
    const ped = PlayerPedId();
    SetEntityVisible(ped, true, false);
    SetEntityCollision(ped, true, true);
    const gender = data.gender;
    const model = gender === 'male' ? 'mp_m_freemode_01' : 'mp_f_freemode_01';
    RequestModel(model);
    while (!HasModelLoaded(model)) await DELAY(0);
    SetPlayerModel(PlayerId(), 'mp_m_freemode_01');


    cb({ ok: true }); // Acknowledge the callback
});

RegisterNuiCallback('submitNewCharacter', (data: any, cb: (res: any) => void) => {
    emitNet('framework:createCharacter', data);

    // Hide the UI to give feedback that something is happening.
    // It will be shown again by the 'framework:showCharacterSelection' event from the server.
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: 'hideCharacterSelection' }));

    cb({ ok: true });
});

RegisterNuiCallback('deleteCharacter', (data: any, cb: (res: any) => void) => {
    console.log('Requesting to delete character:', data);
    emitNet('framework:server_deleteCharacter', data);

    // Hide the UI to give feedback that something is happening.
    // It will be shown again by the 'framework:showCharacterSelection' event from the server.
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: 'hideCharacterSelection' }));

    cb({ ok: true });
});


RegisterNuiCallback('createCharacter', (data: any, cb: (res: any) => void) => {
   
    SendNuiMessage(JSON.stringify({ action: 'showCreateForm' }));
    cb({ ok: true });
});
