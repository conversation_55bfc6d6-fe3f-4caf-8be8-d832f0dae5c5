/**
 * List of cloud hats. Used to change cloud patterns
 */
export var CloudHat;
(function (CloudHat) {
    CloudHat[CloudHat["Unknown"] = 1] = "Unknown";
    CloudHat[CloudHat["Altostratus"] = 2] = "Altostratus";
    CloudHat[CloudHat["Cirrus"] = 3] = "Cirrus";
    CloudHat[CloudHat["Cirrocumulus"] = 4] = "Cirrocumulus";
    CloudHat[CloudHat["Clear"] = 5] = "Clear";
    CloudHat[CloudHat["Cloudy"] = 6] = "Cloudy";
    CloudHat[CloudHat["Contrails"] = 7] = "Contrails";
    CloudHat[CloudHat["Horizon"] = 8] = "Horizon";
    CloudHat[CloudHat["HorizonBand1"] = 9] = "HorizonBand1";
    CloudHat[CloudHat["HorizonBand2"] = 10] = "HorizonBand2";
    CloudHat[CloudHat["HorizonBand3"] = 11] = "HorizonBand3";
    CloudHat[CloudHat["Horsey"] = 12] = "Horsey";
    CloudHat[CloudHat["Nimbus"] = 13] = "Nimbus";
    CloudHat[CloudHat["Puffs"] = 14] = "Puffs";
    CloudHat[CloudHat["Rain"] = 15] = "Rain";
    CloudHat[CloudHat["Snowy"] = 16] = "Snowy";
    CloudHat[CloudHat["Stormy"] = 17] = "Stormy";
    CloudHat[CloudHat["Stratoscumulus"] = 18] = "Stratoscumulus";
    CloudHat[CloudHat["Stripey"] = 19] = "Stripey";
    CloudHat[CloudHat["Shower"] = 20] = "Shower";
    CloudHat[CloudHat["Wispy"] = 21] = "Wispy";
})(CloudHat || (CloudHat = {}));
