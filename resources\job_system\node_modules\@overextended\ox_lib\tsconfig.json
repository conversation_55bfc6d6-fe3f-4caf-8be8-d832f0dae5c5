{"compilerOptions": {"baseUrl": ".", "strict": true, "module": "es2022", "types": ["@types/node", "@citizenfx/client", "@citizenfx/server"], "declaration": true, "target": "esnext", "allowJs": false, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": true}, "include": ["client", "server", "shared"], "exclude": ["**/node_modules"]}