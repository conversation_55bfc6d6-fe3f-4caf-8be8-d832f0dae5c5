import { waitFor } from '../../';
function streamingRequest(request, hasLoaded, assetType, asset, timeout = 30000, ...args) {
    if (hasLoaded(asset))
        return asset;
    request(asset, ...args);
    return waitFor(() => {
        if (hasLoaded(asset))
            return asset;
    }, `failed to load ${assetType} '${asset}' - this may be caused by\n- too many loaded assets\n- oversized, invalid, or corrupted assets`, timeout);
}
export const requestAnimDict = (animDict, timeout) => {
    if (!DoesAnimDictExist(animDict))
        throw new Error(`attempted to load invalid animDict '${animDict}'`);
    return streamingRequest(RequestAnimDict, HasAnimDictLoaded, 'animDict', animDict, timeout);
};
export const requestAnimSet = (animSet, timeout) => streamingRequest(RequestAnimSet, HasAnimSetLoaded, 'animSet', animSet, timeout);
export const requestModel = (model, timeout) => {
    if (typeof model !== 'number')
        model = GetHashKey(model);
    if (!IsModelValid(model))
        throw new Error(`attempted to load invalid model '${model}'`);
    return streamingRequest(RequestModel, HasModelLoaded, 'model', model, timeout);
};
export const requestNamedPtfxAsset = (ptFxName, timeout) => streamingRequest(RequestNamedPtfxAsset, HasNamedPtfxAssetLoaded, 'ptFxName', ptFxName, timeout);
export const requestScaleformMovie = (scaleformName, timeout) => streamingRequest(RequestScaleformMovie, HasScaleformMovieLoaded, 'scaleformMovie', scaleformName, timeout);
export const requestStreamedTextureDict = (textureDict, timeout) => streamingRequest(RequestStreamedTextureDict, HasStreamedTextureDictLoaded, 'textureDict', textureDict, timeout);
export const requestWeaponAsset = (weaponHash, timeout, weaponResourceFlags = 31, extraWeaponComponentFlags = 0) => streamingRequest(RequestWeaponAsset, HasWeaponAssetLoaded, 'weaponHash', weaponHash, timeout, weaponResourceFlags, extraWeaponComponentFlags);
