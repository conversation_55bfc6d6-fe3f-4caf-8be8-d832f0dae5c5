import type { Sx } from '@mantine/core';
import type { IconName, IconPrefix } from '@fortawesome/fontawesome-common-types';
type NotificationPosition = 'top' | 'top-right' | 'top-left' | 'bottom' | 'bottom-right' | 'bottom-left' | 'center-right' | 'center-left';
type NotificationType = 'inform' | 'error' | 'success';
type IconAnimation = 'spin' | 'spinPulse' | 'spinReverse' | 'pulse' | 'beat' | 'fade' | 'beatFade' | 'bounce' | 'shake';
interface NotifyProps {
    id?: string | number;
    title?: string;
    description?: string;
    duration?: number;
    showDuration?: boolean;
    position?: NotificationPosition;
    type?: NotificationType;
    style?: Sx;
    icon?: IconName | [IconPrefix, IconName];
    iconColor?: string;
    iconAnimation?: IconAnimation;
    alignIcon?: 'top' | 'center';
    sound?: {
        bank?: string;
        set: string;
        name: string;
    };
}
export declare const notify: (data: NotifyProps) => void;
interface DefaultNotifyProps {
    title?: string;
    description?: string;
    duration?: number;
    position?: NotificationPosition;
    status?: 'info' | 'warning' | 'success' | 'error';
    id?: number;
}
export declare const defaultNotify: (data: DefaultNotifyProps) => void;
export {};
