{"name": "@nativewrappers/client", "version": "1.7.33", "description": "Javascript/Typescript wrapper for the FiveM natives", "sideEffects": ["./src/models/**"], "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/AvarianKnight/native-wrappers-client.git"}, "keywords": ["fivem", "wrapper", "javascript", "typescript", "citizenfx"], "files": ["lib/**/*"], "author": "Remco Troost <d0p3t>", "license": "MIT", "bugs": {"url": "https://github.com/AvarianKnight/native-wrappers-client/issues"}, "homepage": "https://fivemjs.avarian.dev/", "devDependencies": {"@citizenfx/client": "^2.0.5181-1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.1", "prettier": "^2.5.1", "typedoc": "^0.22.10", "typedoc-fivemjs-theme": "^0.2.7", "typescript": "^4.6.4"}, "directories": {"doc": "docs", "example": "examples", "lib": "lib"}, "scripts": {"build": "tsc", "watch": "tsc --watch", "ci": "pnpm run lint && pnpm run build", "lint": "eslint . --ext .ts", "lint-fix": "eslint --fix . --ext .ts", "typedoc": "typedoc", "docs": "npm run typedoc -- --options typedoc.json"}, "readme": "## Notice for breaking changes\r\nThis library is going to be undergoing a rewrite over the next few months, backwards compatibility will not be guaranteed, if you want version thats guaranteed stable please look use 1.7.*\r\n\r\n<h1 align=\"center\">native-wrappers-client</h1>\r\n\r\n<p align=\"center\">\r\n  <i>:fire: A Javascript/Typescript wrapper for the FiveM natives :video_game:</i>\r\n  <br>\r\n  <br>\r\n  <a href=\"https://github.com/AvarianKnight/native-wrappers-client/blob/master/LICENSE\">\r\n    <img src=\"https://img.shields.io/badge/License-MIT-blue.svg?style=flat\" alt=\"License: MIT\">\r\n  </a>\r\n  <a href=\"https://www.npmjs.com/package/@nativewrappers/client\">\r\n    <img src=\"https://img.shields.io/npm/v/@nativewrappers/client?style=flat\" alt=\"npm version\">\r\n  </a>\r\n  <a href=\"https://www.npmjs.com/package/@nativewrappers/client\">\r\n    <img src=\"https://img.shields.io/npm/dm/@nativewrappers/client?style=flat\">\r\n  </a>\r\n  <a href=\"https://github.com/nativewrappers/fivem-client/actions/workflows/config.yml\">\r\n    <img src=\"https://github.com/nativewrappers/fivem-client/actions/workflows/config.yml/badge.svg\" alt=\"Workflow Status\">\r\n  </a>\r\n  <a href=\"https://github.com/AvarianKnight/native-wrappers-client/commits/master\">\r\n    <img src=\"https://img.shields.io/github/last-commit/nativewrappers/fivem-client.svg?style=flat\" alt=\"Last commit\">\r\n  </a>\r\n</p>\r\n\r\n<p align=\"center\">\r\n  <a href=\"https://fivemjs.avarian.dev/\">Documentation</a>\r\n  -\r\n  <a href=\"https://forum.fivem.net/t/fivem-js-v1-3-2-javascript-typescript-wrapper-now-with-menu-class-nativeui/268640\">Forum</a>\r\n</p>\r\n\r\nThis is a continuation of [fivem-js](https://github.com/d0p3t/fivem-js), who's maintainer has sadly passed away.\r\n\r\nThis project is in no way affiliated with FiveM or the Cfx Collective.\r\n\r\nFunctionality of this wrapper is **based on the FiveM C# wrapper** - [link](https://github.com/citizenfx/fivem/tree/master/code/client/clrcore/External). It's a feature-rich set of helper classes, objects, and functions to help you develop your project faster.\r\n\r\n## Features\r\n\r\n- No runtime dependencies\r\n- Abstracts common used FiveM practices\r\n- Entity management through class objects (i.e. `Vehicle` and `Ped` entities)\r\n- UI elements such as `scaleforms` and loading `prompts`\r\n- Audio, Blips, Cameras and more...\r\n\r\nIn other words, whatever the FiveM C# wrapper can do, this package can as well and more!\r\n\r\n## Download & Install\r\n\r\n`yarn add @nativewrappers/client`\r\n\r\nor\r\n\r\n`npm i @nativewrappers/client`\r\n\r\nhttps://www.npmjs.com/package/@nativewrappers/client\r\n\r\n\r\n## Simple Usage\r\n\r\nSee [here](https://github.com/AvarianKnight/native-wrappers-client/tree/master/examples) for example projects.\r\n\r\n### Typescript\r\n\r\n```ts\r\nimport * as Cfx from '@nativewrappers/client';\r\n\r\nRegisterCommand(\r\n  'adder',\r\n  async (source: number, args: string[]) => {\r\n    const vehicle = await Cfx.World.createVehicle(\r\n      new Cfx.Model('adder'),\r\n      new Cfx.Vector3(1, 2, 3),\r\n      4,\r\n    );\r\n    Cfx.Game.PlayerPed.setIntoVehicle(vehicle, Cfx.VehicleSeat.Driver);\r\n  },\r\n  false,\r\n);\r\n```\r\n\r\nYou can also individually import classes.\r\n\r\n```typescript\r\nimport { World } from '@nativewrappers/client/lib/World';\r\n```\r\n\r\n### Javascript\r\n\r\n```js\r\n/// <reference path=\"node_modules/@nativewrappers/client/lib/index.d.ts\"/>\r\n/// <reference path=\"node_modules/@citizenfx/client/natives_universal.d.ts\"/>\r\n\r\nconst Cfx = require('@nativewrappers/client');\r\n\r\nRegisterCommand(\r\n  'adder',\r\n  async (source, args) => {\r\n    const vehicle = await Cfx.World.createVehicle(\r\n      new Cfx.Model('adder'),\r\n      new Cfx.Vector3(1, 2, 3),\r\n      4,\r\n    );\r\n    Cfx.Game.PlayerPed.setIntoVehicle(vehicle, Cfx.VehicleSeat.Driver);\r\n  },\r\n  false,\r\n);\r\n```\r\n\r\n## Contributing\r\n\r\nYou are more than welcome to contribute to this project by submitting a pull request and creating issues.\r\n\r\nPlease checkout [CONTRIBUTING.md](./CONTRIBUTING.md) for our contributing guidelines.\r\n\r\n## License\r\n\r\nMIT with customization. See [LICENSE](https://github.com/AvarianKnight/native-wrappers-client/blob/master/LICENSE)\r\n"}