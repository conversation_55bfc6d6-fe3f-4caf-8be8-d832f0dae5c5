interface AlertDialogProps {
    header: string;
    content: string;
    centered?: boolean;
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    overflow?: boolean;
    cancel?: boolean;
    labels?: {
        cancel?: string;
        confirm?: string;
    };
}
type alertDialog = (data: AlertDialogProps) => Promise<'cancel' | 'confirm'>;
export declare const alertDialog: alertDialog;
export declare const closeAlertDialog: (reason?: string) => any;
export {};
