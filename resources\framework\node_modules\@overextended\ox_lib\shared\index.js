export * from './resource';
export function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms, null));
}
/**
 * Creates a promise that will be resolved once any value is returned by the function (including null).
 * @param {number?} timeout Error out after `~x` ms. Defaults to 1000, unless set to `false`.
 */
export async function waitFor(cb, errMessage, timeout) {
    let value = await cb();
    if (value !== undefined)
        return value;
    if (timeout || timeout == null) {
        if (typeof timeout !== 'number')
            timeout = 1000;
    }
    const start = GetGameTimer();
    let id;
    const p = new Promise((resolve, reject) => {
        id = setTick(async () => {
            const elapsed = timeout && GetGameTimer() - start;
            if (elapsed && elapsed > timeout) {
                return reject(`${errMessage || 'failed to resolve callback'} (waited ${elapsed}ms)`);
            }
            value = await cb();
            if (value !== undefined)
                resolve(value);
        });
    }).finally(() => clearTick(id));
    return p;
}
export function getRandomInt(min = 0, max = 9) {
    if (min > max)
        [min, max] = [max, min];
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
export function getRandomChar(lowercase) {
    const str = String.fromCharCode(getRandomInt(65, 90));
    return lowercase ? str.toLowerCase() : str;
}
export function getRandomAlphanumeric(lowercase) {
    return Math.random() > 0.5 ? getRandomChar(lowercase) : getRandomInt();
}
const formatChar = {
    '1': getRandomInt,
    A: getRandomChar,
    '.': getRandomAlphanumeric,
    a: getRandomChar,
};
export function getRandomString(pattern, length) {
    const len = length || pattern.replace(/\^/g, '').length;
    const arr = Array(len).fill(0);
    let size = 0;
    let i = 0;
    while (size < len) {
        i += 1;
        let char = pattern.charAt(i - 1);
        if (char === '') {
            arr[size] = ' '.repeat(len - size);
            break;
        }
        else if (char === '^') {
            i += 1;
            char = pattern.charAt(i - 1);
        }
        else {
            const fn = formatChar[char];
            char = fn ? fn(char === 'a') : char;
        }
        size += 1;
        arr[size - 1] = char;
    }
    return arr.join('');
}
