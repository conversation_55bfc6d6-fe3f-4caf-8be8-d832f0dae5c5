/**
 * Creates an HTML element for a single character.
 * @param {object} character - The character data.
 * @returns {HTMLElement} The character item element.
 */
function createCharacterElement(character) {
    const item = document.createElement('div');
    item.className = 'character-item';

    // A placeholder for a character image. You can replace the background-image
    // with a real image URL from your character data if you have one.
    // e.g., `style="background-image: url('${character.imageUrl}')"`
    const portraitStyle = '';

    item.innerHTML = `
        <div class="character-portrait" ${portraitStyle}></div>
        <h2>${character.firstName + ' ' + character.lastName}</h2>
        <div class="actions">
            <button class="delete-button">Delete</button>
            <button class="play-button">Play</button>
        </div>
    `;

    item.querySelector('.delete-button').addEventListener('click', (e) => {
        e.stopPropagation();
        showDeleteConfirmation(character);
    });

    item.querySelector('.play-button').addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent click from bubbling to parent if it has its own listener

        // Send a message back to the Lua script to select this character
        fetch(`https://${GetParentResourceName()}/selectCharacter`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify(character)
        });
    });

    // Allow selecting the character by clicking the whole card, not just the button
    item.addEventListener('click', () => {
        // We can just simulate a click on the button inside this card
        item.querySelector('.play-button').click();
    });

    return item;
}

/**
 * Creates the "Create New Character" option.
 * @returns {HTMLElement} The new character option element.
 */
function createNewCharacterOption() {
    const item = document.createElement('div');
    item.className = 'character-item new-character';

    item.innerHTML = `
        <h2>Create New Character</h2>
        <button class="create-button">Create</button>
    `;

    item.querySelector('.create-button').addEventListener('click', () => {
        // Send a message back to the Lua script to show the character creation screen
        fetch(`https://${GetParentResourceName()}/createCharacter`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        });
    });

    return item;
}

/**
 * Renders the character list.
 * @param {Array<object>} characters - An array of character objects.
 */
function renderCharacterList(characters) {
    const characterList = document.querySelector('.character-list');
    if (!characterList) return;

    // Clear any previous content
    characterList.innerHTML = '';

    // Add each character to the list, ensuring characters is an array.
    if (Array.isArray(characters)) {
        characters.forEach(charData => {
            const charElement = createCharacterElement(charData);
            characterList.appendChild(charElement);
        });
    }

    // Add the "Create New" option
    const newCharOption = createNewCharacterOption();
    characterList.appendChild(newCharOption);
}

let cachedCharacters = [];

function showView(viewName) {
    const selectionContainer = document.querySelector('.container');
    const creationContainer = document.querySelector('.creation-container');

    // Hide all views first
    selectionContainer.style.display = 'none';
    creationContainer.style.display = 'none';

    // Show the requested view
    if (viewName === 'selection') {
        selectionContainer.style.display = 'block';
    } else if (viewName === 'creation') {
        creationContainer.style.display = 'block';
    }

    // Make sure the body is visible
    document.body.style.display = 'flex';
}

window.addEventListener("message", (event) => {
    const data = event.data;

    switch (data.action) {
        case "showCharacterSelection":
            // Make the UI visible and render the characters
            cachedCharacters = data.characters;
            renderCharacterList(cachedCharacters);
            showView('selection');
            break;
        case "hideCharacterSelection":
            // Hide the UI
            document.body.style.display = 'none';
            break;
        case "showCreateForm":
            // Show the character creation form
            showView('creation');
            break;
    }
});

let characterToDelete = null;

function showDeleteConfirmation(character) {
    const dialog = document.getElementById('delete-confirm-dialog');
    characterToDelete = character;
    dialog.style.display = 'flex';
}

function hideDeleteConfirmation() {
    const dialog = document.getElementById('delete-confirm-dialog');
    characterToDelete = null;
    dialog.style.display = 'none';
}

// When the page loads, render the character list.
document.addEventListener('DOMContentLoaded', () => {
    // The UI is hidden by default and is shown via a NUI message.
    // We can let the client script know that the UI is ready to receive messages.
    fetch(`https://${GetParentResourceName()}/uiReady`, {
        method: 'POST',
        body: '{}'
    });

    document.getElementById('back-button').addEventListener('click', () => {
        // Use cached data to show the selection screen without a server round-trip
        renderCharacterList(cachedCharacters);
        showView('selection');
    });

    document.getElementById('cancel-delete-button').addEventListener('click', hideDeleteConfirmation);

    document.getElementById('confirm-delete-button').addEventListener('click', () => {
        if (!characterToDelete) return;

        // Send request to delete character. The client script will handle hiding the UI.
        fetch(`https://${GetParentResourceName()}/deleteCharacter`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify(characterToDelete)
        });

        hideDeleteConfirmation();
    });

    document.getElementById('creation-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const formData = new FormData(e.target);
        const newCharacter = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            gender: formData.get('gender'),
            dob: formData.get('dob'),
        };

        // Basic validation
        if (!newCharacter.firstName || !newCharacter.lastName || !newCharacter.dob || !newCharacter.gender) {
            // You could show an error message to the user here
            console.error("All fields are required!");
            return;
        }

        // Disable the button to prevent multiple submissions
        const submitButton = document.getElementById('create-character-button');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Creating...';
        }



        // Send the new character data to the client script
        fetch(`https://${GetParentResourceName()}/submitNewCharacter`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify(newCharacter)
        }).then(resp => resp.json()).then(() => {
            // The client script will hide the UI. We can reset the form and
            // re-enable the button for the next time it's shown.
            e.target.reset();
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Create Character';
            }
        });
    });
});