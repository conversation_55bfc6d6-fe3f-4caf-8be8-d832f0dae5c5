export var DrivingStyle;
(function (DrivingStyle) {
    DrivingStyle[DrivingStyle["None"] = 0] = "None";
    DrivingStyle[DrivingStyle["Normal"] = 786603] = "Normal";
    DrivingStyle[DrivingStyle["IgnoreLights"] = 2883621] = "IgnoreLights";
    DrivingStyle[DrivingStyle["SometimesOvertakeTraffic"] = 5] = "SometimesOvertakeTraffic";
    DrivingStyle[DrivingStyle["Rushed"] = 1074528293] = "Rushed";
    DrivingStyle[DrivingStyle["AvoidTraffic"] = 786468] = "AvoidTraffic";
    DrivingStyle[DrivingStyle["AvoidTrafficExtremely"] = 6] = "AvoidTrafficExtremely";
    DrivingStyle[DrivingStyle["AvoidHighwaysWhenPossible"] = 536870912] = "AvoidHighwaysWhenPossible";
    DrivingStyle[DrivingStyle["IgnorePathing"] = 16777216] = "IgnorePathing";
    DrivingStyle[DrivingStyle["IgnoreRoads"] = 4194304] = "IgnoreRoads";
    DrivingStyle[DrivingStyle["ShortestPath"] = 262144] = "ShortestPath";
    DrivingStyle[DrivingStyle["Backwards"] = 1024] = "Backwards";
})(DrivingStyle || (DrivingStyle = {}));
export var VehicleDrivingFlags;
(function (VehicleDrivingFlags) {
    VehicleDrivingFlags[VehicleDrivingFlags["None"] = 0] = "None";
    VehicleDrivingFlags[VehicleDrivingFlags["FollowTraffic"] = 1] = "FollowTraffic";
    VehicleDrivingFlags[VehicleDrivingFlags["YieldToPeds"] = 2] = "YieldToPeds";
    VehicleDrivingFlags[VehicleDrivingFlags["AvoidVehicles"] = 4] = "AvoidVehicles";
    VehicleDrivingFlags[VehicleDrivingFlags["AvoidEmptyVehicles"] = 8] = "AvoidEmptyVehicles";
    VehicleDrivingFlags[VehicleDrivingFlags["AvoidPeds"] = 16] = "AvoidPeds";
    VehicleDrivingFlags[VehicleDrivingFlags["AvoidObjects"] = 32] = "AvoidObjects";
    VehicleDrivingFlags[VehicleDrivingFlags["StopAtTrafficLights"] = 128] = "StopAtTrafficLights";
    VehicleDrivingFlags[VehicleDrivingFlags["UseBlinkers"] = 256] = "UseBlinkers";
    VehicleDrivingFlags[VehicleDrivingFlags["AllowGoingWrongWay"] = 512] = "AllowGoingWrongWay";
    VehicleDrivingFlags[VehicleDrivingFlags["Reverse"] = 1024] = "Reverse";
    VehicleDrivingFlags[VehicleDrivingFlags["AllowMedianCrossing"] = 262144] = "AllowMedianCrossing";
    VehicleDrivingFlags[VehicleDrivingFlags["DriveBySight"] = 4194304] = "DriveBySight";
    VehicleDrivingFlags[VehicleDrivingFlags["IgnorePathFinding"] = 16777216] = "IgnorePathFinding";
    VehicleDrivingFlags[VehicleDrivingFlags["TryToAvoidHighways"] = 536870912] = "TryToAvoidHighways";
    VehicleDrivingFlags[VehicleDrivingFlags["StopAtDestination"] = 2147483648] = "StopAtDestination";
})(VehicleDrivingFlags || (VehicleDrivingFlags = {}));
