exports.ox_target:addModel(
{ `prop_phonebox_01b`, `p_phonebox_01b_s`, `sf_prop_sf_phonebox_01b_straight`, `p_phonebox_02_s`, `prop_phonebox_03`,
    `prop_phonebox_02`, `prop_phonebox_01c`, `prop_phonebox_01a` }, {
    {
        icon = 'fa-solid fa-id-badge',
        label = "Find Labor Job",
        distance = 4.0,
        event = 'ollama_ped:interact_with_model_start',
    },
    -- {
    --     icon = 'fa-solid fa-trash-alt',
    --     label = "Delete Chalkboard",
    --     event = 'chalkboards:client:deleteChalkboard',
    --     distance = 4.0,
    --     onSelect = function(data)

    --     end
    -- }
})

local clothing_store_zones = {
    {title="Clothing Store", colour=38, id=73, x = 1693.45667, y = 4823.17725, z = 42.163129, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -712.215881,y = -155.352982, z = 37.415126 ,radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -1192.94495,y = -772.688965, z = 17.3255997, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 425.236,y = -806.008,z = 28.491, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -162.658,y = -303.397,z = 38.733, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 75.950,y = -1392.891,z = 28.376, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -822.194,y = -1074.134,z = 10.328, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -1450.711,y = -236.83,z = 48.809, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 4.254,y = 6512.813,z = 30.877, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 615.180,y = 2762.933,z = 41.088, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 1196.785,y = 2709.558,z = 37.22, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -3171.453,y = 1043.857,z = 19.86, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -1100.959,y = 2710.211,z = 18.10, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 121.76,y = -224.6,z = 53.5, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 1780.449, y = 2547.869, z = 44.798, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = 1849.555, y = 3695.773, z = 33.3, radius = 5.0},
    {title="Clothing Store", colour=38, id=73, x = -454.379, y = 6014.986, z = 30.7, radius = 5.0}
  }

  Citizen.CreateThread(function()

    for _, info in pairs(clothing_store_zones) do
      info.blip = AddBlipForCoord(info.x, info.y, info.z)
      SetBlipSprite(info.blip, info.id)
      SetBlipDisplay(info.blip, 4)
      SetBlipScale(info.blip, 0.7)
      SetBlipColour(info.blip, info.colour)
      SetBlipAsShortRange(info.blip, true)
	  BeginTextCommandSetBlipName("STRING")
      AddTextComponentString(info.title)
      EndTextCommandSetBlipName(info.blip)
    end
end)

RegisterNetEvent('framework:openClothingStore', function()
  local config = {
    ped = true,
    headBlend = true,
    faceFeatures = true,
    headOverlays = true,
    components = true,
    props = true,
    tattoos = false,
    allowExit = true
  }

  exports['fivem-appearance']:startPlayerCustomization(function(appearance)
    if appearance then
      local char = exports.framework:getCurrentCharacter()

      if char and char.id then
        -- The framework already has an event to save appearance data
        TriggerServerEvent('framework:saveAppearanceData', char.id, json.encode(appearance))
        lib.notify({ title = 'Appearance Saved', type = 'success' })
      end
    else
      lib.notify({ title = 'Canceled', type = 'info' })
    end
  end, config)
end)

  for _, zone in ipairs(clothing_store_zones) do
    exports.ox_target:addBoxZone({
      coords = vec3(zone.x, zone.y, zone.z),
      size = vec3(1, 1, 1),
      rotation = 0,
      debug = true,
      options = {
        {
          icon = 'fa-solid fa-id-badge',
          label = zone.title,
          distance = 4.0,
          event = 'framework:openClothingStore',
        },
      }
    })
  end
