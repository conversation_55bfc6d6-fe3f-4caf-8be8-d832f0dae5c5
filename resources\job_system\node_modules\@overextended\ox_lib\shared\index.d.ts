export * from './resource';
export type FlattenObjectKeys<T extends Record<string, any>, Key = keyof T> = Key extends string ? T[Key] extends Record<string, unknown> ? `${Key}.${FlattenObjectKeys<T[Key]>}` : `${Key}` : never;
export declare function sleep(ms: number): Promise<unknown>;
export interface VehicleProperties {
    model: string;
    plate: string;
    plateIndex: number;
    bodyHealth: number;
    engineHealth: number;
    tankHealth: number;
    fuelLevel: number;
    oilLevel: number;
    dirtLevel: number;
    paintType1: number;
    paintType2: number;
    color1: number | [number, number, number];
    color2: number | [number, number, number];
    pearlescentColor: number;
    interiorColor: number;
    dashboardColor: number;
    wheelColor: number;
    wheelWidth: number;
    wheelSize: number;
    wheels: number;
    windowTint: number;
    xenonColor: number;
    neonEnabled: boolean[];
    neonColor: [number, number, number];
    extras: Record<number | string, 0 | 1>;
    tyreSmokeColor: [number, number, number];
    modSpoilers: number;
    modFrontBumper: number;
    modRearBumper: number;
    modSideSkirt: number;
    modExhaust: number;
    modFrame: number;
    modGrille: number;
    modHood: number;
    modFender: number;
    modRightFender: number;
    modRoof: number;
    modEngine: number;
    modBrakes: number;
    modTransmission: number;
    modHorns: number;
    modSuspension: number;
    modArmor: number;
    modNitrous: number;
    modTurbo: boolean;
    modSubwoofer: boolean;
    modSmokeEnabled: boolean;
    modHydraulics: boolean;
    modXenon: boolean;
    modFrontWheels: number;
    modBackWheels: number;
    modCustomTiresF: boolean;
    modCustomTiresR: boolean;
    modPlateHolder: number;
    modVanityPlate: number;
    modTrimA: number;
    modOrnaments: number;
    modDashboard: number;
    modDial: number;
    modDoorSpeaker: number;
    modSeats: number;
    modSteeringWheel: number;
    modShifterLeavers: number;
    modAPlate: number;
    modSpeakers: number;
    modTrunk: number;
    modHydrolic: number;
    modEngineBlock: number;
    modAirFilter: number;
    modStruts: number;
    modArchCover: number;
    modAerials: number;
    modTrimB: number;
    modTank: number;
    modWindows: number;
    modDoorR: number;
    modLivery: number;
    modRoofLivery: number;
    modLightbar: number;
    windows: number[];
    doors: number[];
    tyres: Record<number | string, 1 | 2>;
    leftHeadlight: boolean;
    rightHeadlight: boolean;
    frontBumper: boolean;
    rearBumper: boolean;
    bulletProofTyres: boolean;
    driftTyres: boolean;
}
/**
 * Creates a promise that will be resolved once any value is returned by the function (including null).
 * @param {number?} timeout Error out after `~x` ms. Defaults to 1000, unless set to `false`.
 */
export declare function waitFor<T>(cb: () => T, errMessage?: string, timeout?: number | false): Promise<T>;
export declare function getRandomInt(min?: number, max?: number): number;
export declare function getRandomChar(lowercase?: boolean): string;
export declare function getRandomAlphanumeric(lowercase?: boolean): string | number;
export declare function getRandomString(pattern: string, length?: number): string;
