import { Db, MongoClient } from "mongodb";

let DATABASE: Db | null = null;


export async function CreateDocument(collection: string, document: object) {
    const database = ReturnDatabase();
    if (!database) {
        console.error("[framework:database] Database not connected. Cannot create document.");
        return;
    }
    await database.collection(collection).insertOne(document);
}


export async function ReadDocuments(collection: string, query: object) {
    const database = ReturnDatabase();
    if (!database) {
        console.error("[framework:database] Database not connected. Cannot read documents.");
        return null;
    }
    try {
        const data = await database.collection(collection).find(query).toArray();
        // console.log(data.length > 0 ? data : "No data found for query.");
        return data;
    } catch (e) {
        console.error(`[framework:database] Error reading documents from '${collection}':`, e);
        return null;
    }
}

export async function UpdateDocument(collection: string, filter: object, update: object) {
    const database = ReturnDatabase();
    if (!database) {
        console.error("[framework:database] Database not connected. Cannot update document.");
        return;
    }
    await database.collection(collection).updateOne(filter, update);
}

export async function DeleteDocument(collection: string, filter: object) {
    const database = ReturnDatabase();
    if (!database) {
        console.error("[framework:database] Database not connected. Cannot delete document.");
        return;
    }
    await database.collection(collection).deleteOne(filter);
}


export function DatabaseConnect() {
    const connectionString = GetConvar('database:connection', '');
    if (!connectionString) {
        return;
    }
    const client = new MongoClient(connectionString);
    client.connect().then(async () => {
        DATABASE = client.db('roleplay');
    }).catch((err) => {
        console.log(err);
    });
}

export function ReturnDatabase() {
    if (!DATABASE) {
        return null;
    }
    return DATABASE as Db;
}

exports('ReadDocuments', ReadDocuments);
exports('CreateDocument', CreateDocument);
exports('ReturnDatabase', ReturnDatabase);
exports('UpdateDocument', UpdateDocument);
exports('DeleteDocument', DeleteDocument);