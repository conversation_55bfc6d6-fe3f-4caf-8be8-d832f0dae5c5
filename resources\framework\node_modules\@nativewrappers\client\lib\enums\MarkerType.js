/**
 * List of markers. Markers are 3D visual objects in the world.
 *
 * See native [DRAW_MARKER](https://docs.fivem.net/game-references/markers/) for pictures.
 */
export var MarkerType;
(function (MarkerType) {
    MarkerType[MarkerType["UpsideDownCone"] = 0] = "UpsideDownCone";
    MarkerType[MarkerType["VerticalCylinder"] = 1] = "VerticalCylinder";
    MarkerType[MarkerType["ThickChevronUp"] = 2] = "ThickChevronUp";
    MarkerType[MarkerType["ThinChevronUp"] = 3] = "ThinChevronUp";
    MarkerType[MarkerType["CheckeredFlagRect"] = 4] = "CheckeredFlagRect";
    MarkerType[MarkerType["CheckeredFlagCircle"] = 5] = "CheckeredFlagCircle";
    MarkerType[MarkerType["VerticleCircle"] = 6] = "VerticleCircle";
    MarkerType[MarkerType["PlaneModel"] = 7] = "PlaneModel";
    MarkerType[MarkerType["LostMCDark"] = 8] = "LostMCDark";
    MarkerType[MarkerType["LostMCLight"] = 9] = "LostMCLight";
    MarkerType[MarkerType["Number0"] = 10] = "Number0";
    MarkerType[MarkerType["Number1"] = 11] = "Number1";
    MarkerType[MarkerType["Number2"] = 12] = "Number2";
    MarkerType[MarkerType["Number3"] = 13] = "Number3";
    MarkerType[MarkerType["Number4"] = 14] = "Number4";
    MarkerType[MarkerType["Number5"] = 15] = "Number5";
    MarkerType[MarkerType["Number6"] = 16] = "Number6";
    MarkerType[MarkerType["Number7"] = 17] = "Number7";
    MarkerType[MarkerType["Number8"] = 18] = "Number8";
    MarkerType[MarkerType["Number9"] = 19] = "Number9";
    MarkerType[MarkerType["ChevronUpx1"] = 20] = "ChevronUpx1";
    MarkerType[MarkerType["ChevronUpx2"] = 21] = "ChevronUpx2";
    MarkerType[MarkerType["ChevronUpx3"] = 22] = "ChevronUpx3";
    MarkerType[MarkerType["HorizontalCircleFat"] = 23] = "HorizontalCircleFat";
    MarkerType[MarkerType["ReplayIcon"] = 24] = "ReplayIcon";
    MarkerType[MarkerType["HorizontalCircleSkinny"] = 25] = "HorizontalCircleSkinny";
    MarkerType[MarkerType["HorizontalCircleSkinnyArrow"] = 26] = "HorizontalCircleSkinnyArrow";
    MarkerType[MarkerType["HorizontalSplitArrowCircle"] = 27] = "HorizontalSplitArrowCircle";
    MarkerType[MarkerType["DebugSphere"] = 28] = "DebugSphere";
    MarkerType[MarkerType["DollarSign"] = 29] = "DollarSign";
    MarkerType[MarkerType["HorizontalBars"] = 30] = "HorizontalBars";
    MarkerType[MarkerType["WolfHead"] = 31] = "WolfHead";
    MarkerType[MarkerType["QuestionMark"] = 32] = "QuestionMark";
    MarkerType[MarkerType["PlaneSymbol"] = 33] = "PlaneSymbol";
    MarkerType[MarkerType["HelicopterSymbol"] = 34] = "HelicopterSymbol";
    MarkerType[MarkerType["BoatSymbol"] = 35] = "BoatSymbol";
    MarkerType[MarkerType["CarSymbol"] = 36] = "CarSymbol";
    MarkerType[MarkerType["MotorcycleSymbol"] = 37] = "MotorcycleSymbol";
    MarkerType[MarkerType["BikeSymbol"] = 38] = "BikeSymbol";
    MarkerType[MarkerType["TruckSymbol"] = 39] = "TruckSymbol";
    MarkerType[MarkerType["ParachuteSymbol"] = 40] = "ParachuteSymbol";
    MarkerType[MarkerType["SawbladeSymbol"] = 41] = "SawbladeSymbol";
})(MarkerType || (MarkerType = {}));
