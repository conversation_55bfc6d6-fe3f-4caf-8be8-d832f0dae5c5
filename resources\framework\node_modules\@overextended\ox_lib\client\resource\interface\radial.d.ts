import type { IconName, IconPrefix } from '@fortawesome/fontawesome-common-types';
type RadialItem = {
    id: string;
    label: string;
    icon: IconName | [IconPrefix, IconName] | string;
    onSelect?: (currentMenu: string | null, itemIndex: number) => void | string;
    menu?: string;
    iconWidth?: number;
    iconHeight?: number;
};
export declare const addRadialItem: (items: RadialItem | RadialItem[]) => any;
export declare const removeRadialItem: (item: string) => any;
export declare const registerRadial: (radial: {
    id: string;
    items: Omit<RadialItem, 'id'>[];
}) => any;
export declare const getCurrentRadialId: () => any;
export declare const hideRadial: () => any;
export declare const disableRadial: (state: boolean) => any;
export {};
