/**
 * List of possible entity intersections. Used for raycasting.
 */
export var IntersectOptions;
(function (IntersectOptions) {
    IntersectOptions[IntersectOptions["Everything"] = -1] = "Everything";
    IntersectOptions[IntersectOptions["None"] = 0] = "None";
    IntersectOptions[IntersectOptions["World"] = 1] = "World";
    IntersectOptions[IntersectOptions["Vehicles"] = 2] = "Vehicles";
    IntersectOptions[IntersectOptions["PedsSimpleCollision"] = 4] = "PedsSimpleCollision";
    IntersectOptions[IntersectOptions["Peds"] = 8] = "Peds";
    IntersectOptions[IntersectOptions["Objects"] = 16] = "Objects";
    IntersectOptions[IntersectOptions["Water"] = 32] = "Water";
    IntersectOptions[IntersectOptions["Unk3"] = 128] = "Unk3";
    IntersectOptions[IntersectOptions["Foliage"] = 256] = "Foliage";
})(IntersectOptions || (IntersectOptions = {}));
