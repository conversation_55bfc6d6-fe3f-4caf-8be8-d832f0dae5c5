export declare enum CargobobHook {
    Hook = 0,
    Magnet = 1
}
export declare enum LicensePlateStyle {
    BlueOnWhite1 = 3,
    BlueOnWhite2 = 0,
    BlueOnWhite3 = 4,
    YellowOnBlack = 1,
    YellowOnBlue = 2,
    NorthYankton = 5
}
export declare enum LicensePlateType {
    FrontAndRearPlates = 0,
    FrontPlate = 1,
    RearPlate = 2,
    None = 3
}
export declare enum VehicleClass {
    Compacts = 0,
    Sedans = 1,
    SUVs = 2,
    Coupes = 3,
    Muscle = 4,
    SportsClassics = 5,
    Sports = 6,
    Super = 7,
    Motorcycles = 8,
    OffRoad = 9,
    Industrial = 10,
    Utility = 11,
    Vans = 12,
    Cycles = 13,
    Boats = 14,
    Helicopters = 15,
    Planes = 16,
    Service = 17,
    Emergency = 18,
    Military = 19,
    Commercial = 20,
    Trains = 21
}
export declare enum VehicleColor {
    MetallicBlack = 0,
    MetallicGraphiteBlack = 1,
    MetallicBlackSteel = 2,
    MetallicDarkSilver = 3,
    MetallicSilver = 4,
    MetallicBlueSilver = 5,
    MetallicSteelGray = 6,
    MetallicShadowSilver = 7,
    MetallicStoneSilver = 8,
    MetallicMidnightSilver = 9,
    MetallicGunMetal = 10,
    MetallicAnthraciteGray = 11,
    MatteBlack = 12,
    MatteGray = 13,
    MatteLightGray = 14,
    UtilBlack = 15,
    UtilBlackPoly = 16,
    UtilDarksilver = 17,
    UtilSilver = 18,
    UtilGunMetal = 19,
    UtilShadowSilver = 20,
    WornBlack = 21,
    WornGraphite = 22,
    WornSilverGray = 23,
    WornSilver = 24,
    WornBlueSilver = 25,
    WornShadowSilver = 26,
    MetallicRed = 27,
    MetallicTorinoRed = 28,
    MetallicFormulaRed = 29,
    MetallicBlazeRed = 30,
    MetallicGracefulRed = 31,
    MetallicGarnetRed = 32,
    MetallicDesertRed = 33,
    MetallicCabernetRed = 34,
    MetallicCandyRed = 35,
    MetallicSunriseOrange = 36,
    MetallicClassicGold = 37,
    MetallicOrange = 38,
    MatteRed = 39,
    MatteDarkRed = 40,
    MatteOrange = 41,
    MatteYellow = 42,
    UtilRed = 43,
    UtilBrightRed = 44,
    UtilGarnetRed = 45,
    WornRed = 46,
    WornGoldenRed = 47,
    WornDarkRed = 48,
    MetallicDarkGreen = 49,
    MetallicRacingGreen = 50,
    MetallicSeaGreen = 51,
    MetallicOliveGreen = 52,
    MetallicGreen = 53,
    MetallicGasolineBlueGreen = 54,
    MatteLimeGreen = 55,
    UtilDarkGreen = 56,
    UtilGreen = 57,
    WornDarkGreen = 58,
    WornGreen = 59,
    WornSeaWash = 60,
    MetallicMidnightBlue = 61,
    MetallicDarkBlue = 62,
    MetallicSaxonyBlue = 63,
    MetallicBlue = 64,
    MetallicMarinerBlue = 65,
    MetallicHarborBlue = 66,
    MetallicDiamondBlue = 67,
    MetallicSurfBlue = 68,
    MetallicNauticalBlue = 69,
    MetallicBrightBlue = 70,
    MetallicPurpleBlue = 71,
    MetallicSpinnakerBlue = 72,
    MetallicUltraBlue = 73,
    UtilDarkBlue = 75,
    UtilMidnightBlue = 76,
    UtilBlue = 77,
    UtilSeaFoamBlue = 78,
    UtilLightningBlue = 79,
    UtilMauiBluePoly = 80,
    UtilBrightBlue = 81,
    MatteDarkBlue = 82,
    MatteBlue = 83,
    MatteMidnightBlue = 84,
    WornDarkBlue = 85,
    WornBlue = 86,
    WornLightBlue = 87,
    MetallicTaxiYellow = 88,
    MetallicRaceYellow = 89,
    MetallicBronze = 90,
    MetallicYellowBird = 91,
    MetallicLime = 92,
    MetallicChampagne = 93,
    MetallicPuebloBeige = 94,
    MetallicDarkIvory = 95,
    MetallicChocoBrown = 96,
    MetallicGoldenBrown = 97,
    MetallicLightBrown = 98,
    MetallicStrawBeige = 99,
    MetallicMossBrown = 100,
    MetallicBistonBrown = 101,
    MetallicBeechwood = 102,
    MetallicDarkBeechwood = 103,
    MetallicChocoOrange = 104,
    MetallicBeachSand = 105,
    MetallicSunBleechedSand = 106,
    MetallicCream = 107,
    UtilBrown = 108,
    UtilMediumBrown = 109,
    UtilLightBrown = 110,
    MetallicWhite = 111,
    MetallicFrostWhite = 112,
    WornHoneyBeige = 113,
    WornBrown = 114,
    WornDarkBrown = 115,
    WornStrawBeige = 116,
    BrushedSteel = 117,
    BrushedBlackSteel = 118,
    BrushedAluminium = 119,
    Chrome = 120,
    WornOffWhite = 121,
    UtilOffWhite = 122,
    WornOrange = 123,
    WornLightOrange = 124,
    MetallicSecuricorGreen = 125,
    WornTaxiYellow = 126,
    PoliceCarBlue = 127,
    MatteGreen = 128,
    MatteBrown = 129,
    MatteWhite = 131,
    WornWhite = 132,
    WornOliveArmyGreen = 133,
    PureWhite = 134,
    HotPink = 135,
    Salmonpink = 136,
    MetallicVermillionPink = 137,
    Orange = 138,
    Green = 139,
    Blue = 140,
    MettalicBlackBlue = 141,
    MetallicBlackPurple = 142,
    MetallicBlackRed = 143,
    HunterGreen = 144,
    MetallicPurple = 145,
    MetaillicVDarkBlue = 146,
    ModshopBlack1 = 147,
    MattePurple = 148,
    MatteDarkPurple = 149,
    MetallicLavaRed = 150,
    MatteForestGreen = 151,
    MatteOliveDrab = 152,
    MatteDesertBrown = 153,
    MatteDesertTan = 154,
    MatteFoliageGreen = 155,
    DefaultAlloyColor = 156,
    EpsilonBlue = 157,
    PureGold = 158,
    BrushedGold = 159
}
export declare enum VehicleLandingGearState {
    Deployed = 0,
    Closing = 1,
    Opening = 2,
    Retracted = 3
}
export declare enum VehicleLockStatus {
    None = 0,
    Unlocked = 1,
    Locked = 2,
    LockedForPlayer = 3,
    StickPlayerInside = 4,
    CanBeBrokenInto = 7,
    CanBeBrokenIntoPersist = 8,
    CannotBeTriedToEnter = 10
}
export declare enum VehicleNeonLight {
    Left = 0,
    Right = 1,
    Front = 2,
    Back = 3
}
export declare enum VehicleRoofState {
    Closed = 0,
    Opening = 1,
    Opened = 2,
    Closing = 3
}
export declare enum VehicleSeat {
    None = -3,
    Any = -2,
    Driver = -1,
    Passenger = 0,
    LeftFront = -1,
    RightFront = 0,
    LeftRear = 1,
    RightRear = 2,
    ExtraSeat1 = 3,
    ExtraSeat2 = 4,
    ExtraSeat3 = 5,
    ExtraSeat4 = 6,
    ExtraSeat5 = 7,
    ExtraSeat6 = 8,
    ExtraSeat7 = 9,
    ExtraSeat8 = 10,
    ExtraSeat9 = 11,
    ExtraSeat10 = 12,
    ExtraSeat11 = 13,
    ExtraSeat12 = 14
}
export declare enum VehicleWindowTint {
    None = 0,
    PureBlack = 1,
    DarkSmoke = 2,
    LightSmoke = 3,
    Stock = 4,
    Limo = 5,
    Green = 6
}
export declare enum VehicleWindowIndex {
    FrontRightWindow = 1,
    FrontLeftWindow = 0,
    BackRightWindow = 3,
    BackLeftWindow = 2,
    ExtraWindow1 = 4,
    ExtraWindow2 = 5,
    ExtraWindow3 = 6,
    ExtraWindow4 = 7
}
export declare enum VehicleModType {
    Spoilers = 0,
    FrontBumper = 1,
    RearBumper = 2,
    SideSkirt = 3,
    Exhaust = 4,
    Frame = 5,
    Grille = 6,
    Hood = 7,
    Fender = 8,
    RightFender = 9,
    Roof = 10,
    Engine = 11,
    Brakes = 12,
    Transmission = 13,
    Horns = 14,
    Suspension = 15,
    Armor = 16,
    FrontWheel = 23,
    RearWheel = 24,
    PlateHolder = 25,
    VanityPlates = 26,
    TrimDesign = 27,
    Ornaments = 28,
    Dashboard = 29,
    DialDesign = 30,
    DoorSpeakers = 31,
    Seats = 32,
    SteeringWheels = 33,
    ColumnShifterLevers = 34,
    Plaques = 35,
    Speakers = 36,
    Trunk = 37,
    Hydraulics = 38,
    EngineBlock = 39,
    AirFilter = 40,
    Struts = 41,
    ArchCover = 42,
    Aerials = 43,
    Trim = 44,
    Tank = 45,
    Windows = 46,
    Livery = 48
}
export declare enum VehicleToggleModType {
    Turbo = 18,
    TireSmoke = 20,
    XenonHeadlights = 22
}
export declare enum VehiclePaintType {
    Normal = 0,
    Metallic = 1,
    Pearl = 2,
    Matte = 3,
    Metal = 4,
    Chrome = 5
}
export declare enum VehicleDoorIndex {
    FrontRightDoor = 1,
    FrontLeftDoor = 0,
    BackRightDoor = 3,
    BackLeftDoor = 2,
    Hood = 4,
    Trunk = 5
}
export declare enum VehicleWheelType {
    Sport = 0,
    Muscle = 1,
    Lowrider = 2,
    SUV = 3,
    Offroad = 4,
    Tuner = 5,
    BikeWheels = 6,
    HighEnd = 7,
    BennysOriginals = 8,
    BennysBespoke = 9
}
export declare enum VehicleWheelIndex {
    FrontLeftWheel = 0,
    FrontRightWheel = 1,
    MidLeftWheel = 2,
    MidRightWheel = 3,
    RearLeftWheel = 4,
    RearRightWheel = 5,
    TrailerMidLeftWheel = 45,
    TrailerMidRightWheel = 47
}
