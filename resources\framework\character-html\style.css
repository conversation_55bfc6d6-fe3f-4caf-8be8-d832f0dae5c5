@import url('https://fonts.googleapis.com/css2?family=Exo+2:wght@400;700;900&display=swap');

:root {
    --background-color: #1a1a1e;
    --primary-color: #25252b;
    --accent-color: #e63946;
    --text-color: #f1f1f1;
    --text-muted-color: #a8a8a8;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Exo 2', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    display: none; /* Hidden by default, shown via NUI message */
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow: hidden;
    /* A subtle background texture can be added here */
    background-image:
        linear-gradient(rgba(26, 26, 30, 0.95), rgba(26, 26, 30, 0.95));
    background-size: cover;
    background-position: center;
}

.container {
    width: 90%;
    max-width: 1400px;
    text-align: center;
}

h1 {
    font-size: 3rem;
    font-weight: 900;
    text-transform: uppercase;
    color: var(--text-color);
    margin-bottom: 40px;
    text-shadow: 0 0 10px var(--accent-color), 0 0 20px var(--accent-color);
}

.character-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    justify-content: center;
}

.character-item {
    background-color: var(--primary-color);
    border: 2px solid transparent;
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, border-color 0.3s ease;
    cursor: pointer;
    clip-path: polygon(0 5%, 100% 0, 100% 95%, 0% 100%);
}

.character-item:before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        transparent,
        rgba(230, 57, 70, 0.2),
        transparent 30%
    );
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.character-item:hover {
    transform: scale(1.05);
    border-color: var(--accent-color);
}

.character-item:hover:before {
    opacity: 1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.character-item .character-portrait {
    width: 120px;
    height: 120px;
    background-color: #333;
    margin: 0 auto 15px auto;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); /* Hexagon */
    background-size: cover;
    background-position: center;
    border: 2px solid var(--accent-color);
}

.character-item h2 {
    font-size: 1.5rem;
    text-transform: uppercase;
    color: var(--text-color);
    margin-bottom: 10px;
}

.character-item p {
    font-size: 0.9rem;
    color: var(--text-muted-color);
    margin-bottom: 5px;
}

.character-item .actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}
.character-item .actions .play-button {
    background-color: var(--accent-color);
}
.character-item .actions .play-button:hover {
    background-color: #ff4757;
}

.character-item .delete-button {
    background-color: #555;
}

.character-item .delete-button:hover {
    background-color: var(--accent-color);
}

.new-character {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-style: dashed;
    border-color: var(--text-muted-color);
}

.new-character:hover {
    border-color: var(--accent-color);
}

.new-character h2 {
    margin-bottom: 20px;
}

/* --- Creation Form Styles --- */

.creation-container {
    width: 90%;
    max-width: 600px;
    background-color: var(--primary-color);
    padding: 40px;
    clip-path: polygon(0 2%, 100% 0, 100% 98%, 0% 100%);
    border: 2px solid var(--accent-color);
    display: none; /* Hidden by default */
}

#creation-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.form-group label {
    font-size: 1rem;
    text-transform: uppercase;
    color: var(--text-muted-color);
    margin-bottom: 8px;
}

.form-group input,
.form-group select {
    background-color: var(--background-color);
    border: 2px solid #444;
    color: var(--text-color);
    padding: 12px;
    font-family: 'Exo 2', sans-serif;
    font-size: 1.1rem;
    transition: border-color 0.3s ease;
    width: 100%;
}

.form-group select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23a8a8a8'%3E%3Cpath d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 24px;
    padding-right: 50px; /* Make space for the arrow */
    cursor: pointer;
}

.form-group select option {
    background: var(--primary-color);
    color: var(--text-color);
}


.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent-color);
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.form-actions button,
.character-item .actions button,
.new-character button,
.dialog-actions button {
    color: var(--text-color);
    border: none;
    padding: 12px 25px;
    font-family: 'Exo 2', sans-serif;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    clip-path: polygon(10% 0, 100% 0, 90% 100%, 0 100%);
}

.form-actions button:hover,
.character-item .actions button:hover,
.new-character button:hover,
.dialog-actions button:hover {
    transform: scale(1.05);
}

.form-actions button[type="submit"] {
    background-color: var(--accent-color);
}

.form-actions button[type="submit"]:hover {
    background-color: #ff4757;
}

.form-actions button[type="button"] {
    background-color: #555;
}

.form-actions button[type="button"]:hover {
    background-color: #777;
}

/* --- Dialog Styles --- */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.75);
    display: none; /* Controlled by JS */
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.dialog-box {
    background-color: var(--primary-color);
    padding: 40px;
    border: 2px solid var(--accent-color);
    clip-path: polygon(0 2%, 100% 0, 100% 98%, 0% 100%);
    text-align: center;
    max-width: 500px;
    width: 90%;
}

.dialog-box h2 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: var(--accent-color);
    text-shadow: 0 0 5px var(--accent-color);
}

.dialog-box p {
    color: var(--text-muted-color);
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.dialog-actions {
    display: flex;
    justify-content: space-around;
}

.new-character button {
    background-color: var(--accent-color);
}
.new-character button:hover {
    background-color: #ff4757;
}

#confirm-delete-button {
    background-color: var(--accent-color);
}
#confirm-delete-button:hover {
    background-color: #ff4757;
}

#cancel-delete-button {
    background-color: #555;
}
#cancel-delete-button:hover {
    background-color: #777;
}
