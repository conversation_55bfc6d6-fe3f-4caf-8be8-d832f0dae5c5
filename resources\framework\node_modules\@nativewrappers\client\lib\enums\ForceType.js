export var ForceType;
(function (ForceType) {
    ForceType[ForceType["MinForce"] = 0] = "MinForce";
    ForceType[ForceType["MaxForceRot"] = 1] = "MaxForceRot";
    ForceType[ForceType["MinForce2"] = 2] = "MinForce2";
    ForceType[ForceType["MaxForceRot2"] = 3] = "MaxForceRot2";
    ForceType[ForceType["ForceNoRot"] = 4] = "ForceNoRot";
    ForceType[ForceType["ForceRotPlusForce"] = 5] = "ForceRotPlusForce";
})(ForceType || (ForceType = {}));
