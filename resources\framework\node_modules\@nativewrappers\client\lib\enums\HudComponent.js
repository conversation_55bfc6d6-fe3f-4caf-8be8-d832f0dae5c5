export var HudComponent;
(function (HudComponent) {
    HudComponent[HudComponent["WantedStars"] = 1] = "WantedStars";
    HudComponent[HudComponent["WeaponIcon"] = 2] = "WeaponIcon";
    HudComponent[HudComponent["Cash"] = 3] = "Cash";
    HudComponent[HudComponent["MpCash"] = 4] = "MpCash";
    HudComponent[HudComponent["MpMessage"] = 5] = "MpMessage";
    HudComponent[HudComponent["VehicleName"] = 6] = "VehicleName";
    HudComponent[HudComponent["AreaName"] = 7] = "AreaName";
    HudComponent[HudComponent["Unused"] = 8] = "Unused";
    HudComponent[HudComponent["StreetName"] = 9] = "StreetName";
    HudComponent[HudComponent["HelpText"] = 10] = "HelpText";
    HudComponent[HudComponent["FloatingHelpText1"] = 11] = "FloatingHelpText1";
    HudComponent[HudComponent["FloatingHelpText2"] = 12] = "FloatingHelpText2";
    HudComponent[HudComponent["CashChange"] = 13] = "CashChange";
    HudComponent[HudComponent["Reticle"] = 14] = "Reticle";
    HudComponent[HudComponent["SubtitleText"] = 15] = "SubtitleText";
    HudComponent[HudComponent["RadioStationsWheel"] = 16] = "RadioStationsWheel";
    HudComponent[HudComponent["Saving"] = 17] = "Saving";
    HudComponent[HudComponent["GamingStreamUnusde"] = 18] = "GamingStreamUnusde";
    HudComponent[HudComponent["WeaponWheel"] = 19] = "WeaponWheel";
    HudComponent[HudComponent["WeaponWheelStats"] = 20] = "WeaponWheelStats";
    HudComponent[HudComponent["DrugsPurse01"] = 21] = "DrugsPurse01";
    HudComponent[HudComponent["DrugsPurse02"] = 22] = "DrugsPurse02";
    HudComponent[HudComponent["DrugsPurse03"] = 23] = "DrugsPurse03";
    HudComponent[HudComponent["DrugsPurse04"] = 24] = "DrugsPurse04";
    HudComponent[HudComponent["MpTagCashFromBank"] = 25] = "MpTagCashFromBank";
    HudComponent[HudComponent["MpTagPackages"] = 26] = "MpTagPackages";
    HudComponent[HudComponent["MpTagCuffKeys"] = 27] = "MpTagCuffKeys";
    HudComponent[HudComponent["MpTagDownloadData"] = 28] = "MpTagDownloadData";
    HudComponent[HudComponent["MpTagIfPedFollowing"] = 29] = "MpTagIfPedFollowing";
    HudComponent[HudComponent["MpTagKeyCard"] = 30] = "MpTagKeyCard";
    HudComponent[HudComponent["MpTagRandomObject"] = 31] = "MpTagRandomObject";
    HudComponent[HudComponent["MpTagRemoteControl"] = 32] = "MpTagRemoteControl";
    HudComponent[HudComponent["MpTagCashFromSafe"] = 33] = "MpTagCashFromSafe";
    HudComponent[HudComponent["MpTagWeaponsPackage"] = 34] = "MpTagWeaponsPackage";
    HudComponent[HudComponent["MpTagKeys"] = 35] = "MpTagKeys";
    HudComponent[HudComponent["MpVehicle"] = 36] = "MpVehicle";
    HudComponent[HudComponent["MpVehicleHeli"] = 37] = "MpVehicleHeli";
    HudComponent[HudComponent["MpVehiclePlane"] = 38] = "MpVehiclePlane";
    HudComponent[HudComponent["PlayerSwitchAlert"] = 39] = "PlayerSwitchAlert";
    HudComponent[HudComponent["MpRankBar"] = 40] = "MpRankBar";
    HudComponent[HudComponent["DirectorMode"] = 41] = "DirectorMode";
    HudComponent[HudComponent["ReplayController"] = 42] = "ReplayController";
    HudComponent[HudComponent["ReplayMouse"] = 43] = "ReplayMouse";
    HudComponent[HudComponent["ReplayHeader"] = 44] = "ReplayHeader";
    HudComponent[HudComponent["ReplayOptions"] = 45] = "ReplayOptions";
    HudComponent[HudComponent["ReplayHelpText"] = 46] = "ReplayHelpText";
    HudComponent[HudComponent["ReplayMiscText"] = 47] = "ReplayMiscText";
    HudComponent[HudComponent["ReplayTopLine"] = 48] = "ReplayTopLine";
    HudComponent[HudComponent["ReplayBottomLine"] = 49] = "ReplayBottomLine";
    HudComponent[HudComponent["ReplayLeftBar"] = 50] = "ReplayLeftBar";
    HudComponent[HudComponent["ReplayTimer"] = 51] = "ReplayTimer";
})(HudComponent || (HudComponent = {}));
