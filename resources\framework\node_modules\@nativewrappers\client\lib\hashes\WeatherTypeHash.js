/**
 * Same list as Weather enum, but as hashes.
 */
export var WeatherTypeHash;
(function (WeatherTypeHash) {
    WeatherTypeHash[WeatherTypeHash["Unknown"] = -1] = "Unknown";
    WeatherTypeHash[WeatherTypeHash["ExtraSunny"] = -1750463879] = "ExtraSunny";
    WeatherTypeHash[WeatherTypeHash["Clear"] = 916995460] = "Clear";
    WeatherTypeHash[WeatherTypeHash["Neutral"] = -1530260698] = "Neutral";
    WeatherTypeHash[WeatherTypeHash["Smog"] = 282916021] = "Smog";
    WeatherTypeHash[WeatherTypeHash["Foggy"] = -1368164796] = "Foggy";
    WeatherTypeHash[WeatherTypeHash["Clouds"] = 821931868] = "Clouds";
    WeatherTypeHash[WeatherTypeHash["Overcast"] = -1148613331] = "Overcast";
    WeatherTypeHash[WeatherTypeHash["Clearing"] = 1840358669] = "Clearing";
    WeatherTypeHash[WeatherTypeHash["Raining"] = 1420204096] = "Raining";
    WeatherTypeHash[WeatherTypeHash["ThunderStorm"] = -1233681761] = "ThunderStorm";
    WeatherTypeHash[WeatherTypeHash["Blizzard"] = 669657108] = "Blizzard";
    WeatherTypeHash[WeatherTypeHash["Snowing"] = -273223690] = "Snowing";
    WeatherTypeHash[WeatherTypeHash["Snowlight"] = 603685163] = "Snowlight";
    WeatherTypeHash[WeatherTypeHash["Christmas"] = -1429616491] = "Christmas";
    WeatherTypeHash[WeatherTypeHash["Halloween"] = -921030142] = "Halloween";
})(WeatherTypeHash || (WeatherTypeHash = {}));
