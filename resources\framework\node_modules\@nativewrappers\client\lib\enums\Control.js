export var Control;
(function (Control) {
    Control[Control["NextCamera"] = 0] = "NextCamera";
    Control[Control["LookLeftRight"] = 1] = "LookLeftRight";
    Control[Control["LookUpDown"] = 2] = "LookUpDown";
    Control[Control["LookUpOnly"] = 3] = "LookUpOnly";
    Control[Control["LookDownOnly"] = 4] = "LookDownOnly";
    Control[Control["LookLeftOnly"] = 5] = "LookLeftOnly";
    Control[Control["LookRightOnly"] = 6] = "LookRightOnly";
    Control[Control["CinematicSlowMo"] = 7] = "CinematicSlowMo";
    Control[Control["FlyUpDown"] = 8] = "FlyUpDown";
    Control[Control["FlyLeftRight"] = 9] = "FlyLeftRight";
    Control[Control["ScriptedFlyZUp"] = 10] = "ScriptedFlyZUp";
    Control[Control["ScriptedFlyZDown"] = 11] = "ScriptedFlyZDown";
    Control[Control["WeaponWheelUpDown"] = 12] = "WeaponWheelUpDown";
    Control[Control["WeaponWheelLeftRight"] = 13] = "WeaponWheelLeftRight";
    Control[Control["WeaponWheelNext"] = 14] = "WeaponWheelNext";
    Control[Control["WeaponWheelPrev"] = 15] = "WeaponWheelPrev";
    Control[Control["SelectNextWeapon"] = 16] = "SelectNextWeapon";
    Control[Control["SelectPrevWeapon"] = 17] = "SelectPrevWeapon";
    Control[Control["SkipCutscene"] = 18] = "SkipCutscene";
    Control[Control["CharacterWheel"] = 19] = "CharacterWheel";
    Control[Control["MultiplayerInfo"] = 20] = "MultiplayerInfo";
    Control[Control["Sprint"] = 21] = "Sprint";
    Control[Control["Jump"] = 22] = "Jump";
    Control[Control["Enter"] = 23] = "Enter";
    Control[Control["Attack"] = 24] = "Attack";
    Control[Control["Aim"] = 25] = "Aim";
    Control[Control["LookBehind"] = 26] = "LookBehind";
    Control[Control["Phone"] = 27] = "Phone";
    Control[Control["SpecialAbility"] = 28] = "SpecialAbility";
    Control[Control["SpecialAbilitySecondary"] = 29] = "SpecialAbilitySecondary";
    Control[Control["MoveLeftRight"] = 30] = "MoveLeftRight";
    Control[Control["MoveUpDown"] = 31] = "MoveUpDown";
    Control[Control["MoveUpOnly"] = 32] = "MoveUpOnly";
    Control[Control["MoveDownOnly"] = 33] = "MoveDownOnly";
    Control[Control["MoveLeftOnly"] = 34] = "MoveLeftOnly";
    Control[Control["MoveRightOnly"] = 35] = "MoveRightOnly";
    Control[Control["Duck"] = 36] = "Duck";
    Control[Control["SelectWeapon"] = 37] = "SelectWeapon";
    Control[Control["Pickup"] = 38] = "Pickup";
    Control[Control["SniperZoom"] = 39] = "SniperZoom";
    Control[Control["SniperZoomInOnly"] = 40] = "SniperZoomInOnly";
    Control[Control["SniperZoomOutOnly"] = 41] = "SniperZoomOutOnly";
    Control[Control["SniperZoomInSecondary"] = 42] = "SniperZoomInSecondary";
    Control[Control["SniperZoomOutSecondary"] = 43] = "SniperZoomOutSecondary";
    Control[Control["Cover"] = 44] = "Cover";
    Control[Control["Reload"] = 45] = "Reload";
    Control[Control["Talk"] = 46] = "Talk";
    Control[Control["Detonate"] = 47] = "Detonate";
    Control[Control["HUDSpecial"] = 48] = "HUDSpecial";
    Control[Control["Arrest"] = 49] = "Arrest";
    Control[Control["AccurateAim"] = 50] = "AccurateAim";
    Control[Control["Context"] = 51] = "Context";
    Control[Control["ContextSecondary"] = 52] = "ContextSecondary";
    Control[Control["WeaponSpecial"] = 53] = "WeaponSpecial";
    Control[Control["WeaponSpecial2"] = 54] = "WeaponSpecial2";
    Control[Control["Dive"] = 55] = "Dive";
    Control[Control["DropWeapon"] = 56] = "DropWeapon";
    Control[Control["DropAmmo"] = 57] = "DropAmmo";
    Control[Control["ThrowGrenade"] = 58] = "ThrowGrenade";
    Control[Control["VehicleMoveLeftRight"] = 59] = "VehicleMoveLeftRight";
    Control[Control["VehicleMoveUpDown"] = 60] = "VehicleMoveUpDown";
    Control[Control["VehicleMoveUpOnly"] = 61] = "VehicleMoveUpOnly";
    Control[Control["VehicleMoveDownOnly"] = 62] = "VehicleMoveDownOnly";
    Control[Control["VehicleMoveLeftOnly"] = 63] = "VehicleMoveLeftOnly";
    Control[Control["VehicleMoveRightOnly"] = 64] = "VehicleMoveRightOnly";
    Control[Control["VehicleSpecial"] = 65] = "VehicleSpecial";
    Control[Control["VehicleGunLeftRight"] = 66] = "VehicleGunLeftRight";
    Control[Control["VehicleGunUpDown"] = 67] = "VehicleGunUpDown";
    Control[Control["VehicleAim"] = 68] = "VehicleAim";
    Control[Control["VehicleAttack"] = 69] = "VehicleAttack";
    Control[Control["VehicleAttack2"] = 70] = "VehicleAttack2";
    Control[Control["VehicleAccelerate"] = 71] = "VehicleAccelerate";
    Control[Control["VehicleBrake"] = 72] = "VehicleBrake";
    Control[Control["VehicleDuck"] = 73] = "VehicleDuck";
    Control[Control["VehicleHeadlight"] = 74] = "VehicleHeadlight";
    Control[Control["VehicleExit"] = 75] = "VehicleExit";
    Control[Control["VehicleHandbrake"] = 76] = "VehicleHandbrake";
    Control[Control["VehicleHotwireLeft"] = 77] = "VehicleHotwireLeft";
    Control[Control["VehicleHotwireRight"] = 78] = "VehicleHotwireRight";
    Control[Control["VehicleLookBehind"] = 79] = "VehicleLookBehind";
    Control[Control["VehicleCinCam"] = 80] = "VehicleCinCam";
    Control[Control["VehicleNextRadio"] = 81] = "VehicleNextRadio";
    Control[Control["VehiclePrevRadio"] = 82] = "VehiclePrevRadio";
    Control[Control["VehicleNextRadioTrack"] = 83] = "VehicleNextRadioTrack";
    Control[Control["VehiclePrevRadioTrack"] = 84] = "VehiclePrevRadioTrack";
    Control[Control["VehicleRadioWheel"] = 85] = "VehicleRadioWheel";
    Control[Control["VehicleHorn"] = 86] = "VehicleHorn";
    Control[Control["VehicleFlyThrottleUp"] = 87] = "VehicleFlyThrottleUp";
    Control[Control["VehicleFlyThrottleDown"] = 88] = "VehicleFlyThrottleDown";
    Control[Control["VehicleFlyYawLeft"] = 89] = "VehicleFlyYawLeft";
    Control[Control["VehicleFlyYawRight"] = 90] = "VehicleFlyYawRight";
    Control[Control["VehiclePassengerAim"] = 91] = "VehiclePassengerAim";
    Control[Control["VehiclePassengerAttack"] = 92] = "VehiclePassengerAttack";
    Control[Control["VehicleSpecialAbilityFranklin"] = 93] = "VehicleSpecialAbilityFranklin";
    Control[Control["VehicleStuntUpDown"] = 94] = "VehicleStuntUpDown";
    Control[Control["VehicleCinematicUpDown"] = 95] = "VehicleCinematicUpDown";
    Control[Control["VehicleCinematicUpOnly"] = 96] = "VehicleCinematicUpOnly";
    Control[Control["VehicleCinematicDownOnly"] = 97] = "VehicleCinematicDownOnly";
    Control[Control["VehicleCinematicLeftRight"] = 98] = "VehicleCinematicLeftRight";
    Control[Control["VehicleSelectNextWeapon"] = 99] = "VehicleSelectNextWeapon";
    Control[Control["VehicleSelectPrevWeapon"] = 100] = "VehicleSelectPrevWeapon";
    Control[Control["VehicleRoof"] = 101] = "VehicleRoof";
    Control[Control["VehicleJump"] = 102] = "VehicleJump";
    Control[Control["VehicleGrapplingHook"] = 103] = "VehicleGrapplingHook";
    Control[Control["VehicleShuffle"] = 104] = "VehicleShuffle";
    Control[Control["VehicleDropProjectile"] = 105] = "VehicleDropProjectile";
    Control[Control["VehicleMouseControlOverride"] = 106] = "VehicleMouseControlOverride";
    Control[Control["VehicleFlyRollLeftRight"] = 107] = "VehicleFlyRollLeftRight";
    Control[Control["VehicleFlyRollLeftOnly"] = 108] = "VehicleFlyRollLeftOnly";
    Control[Control["VehicleFlyRollRightOnly"] = 109] = "VehicleFlyRollRightOnly";
    Control[Control["VehicleFlyPitchUpDown"] = 110] = "VehicleFlyPitchUpDown";
    Control[Control["VehicleFlyPitchUpOnly"] = 111] = "VehicleFlyPitchUpOnly";
    Control[Control["VehicleFlyPitchDownOnly"] = 112] = "VehicleFlyPitchDownOnly";
    Control[Control["VehicleFlyUnderCarriage"] = 113] = "VehicleFlyUnderCarriage";
    Control[Control["VehicleFlyAttack"] = 114] = "VehicleFlyAttack";
    Control[Control["VehicleFlySelectNextWeapon"] = 115] = "VehicleFlySelectNextWeapon";
    Control[Control["VehicleFlySelectPrevWeapon"] = 116] = "VehicleFlySelectPrevWeapon";
    Control[Control["VehicleFlySelectTargetLeft"] = 117] = "VehicleFlySelectTargetLeft";
    Control[Control["VehicleFlySelectTargetRight"] = 118] = "VehicleFlySelectTargetRight";
    Control[Control["VehicleFlyVerticalFlightMode"] = 119] = "VehicleFlyVerticalFlightMode";
    Control[Control["VehicleFlyDuck"] = 120] = "VehicleFlyDuck";
    Control[Control["VehicleFlyAttackCamera"] = 121] = "VehicleFlyAttackCamera";
    Control[Control["VehicleFlyMouseControlOverride"] = 122] = "VehicleFlyMouseControlOverride";
    Control[Control["VehicleSubTurnLeftRight"] = 123] = "VehicleSubTurnLeftRight";
    Control[Control["VehicleSubTurnLeftOnly"] = 124] = "VehicleSubTurnLeftOnly";
    Control[Control["VehicleSubTurnRightOnly"] = 125] = "VehicleSubTurnRightOnly";
    Control[Control["VehicleSubPitchUpDown"] = 126] = "VehicleSubPitchUpDown";
    Control[Control["VehicleSubPitchUpOnly"] = 127] = "VehicleSubPitchUpOnly";
    Control[Control["VehicleSubPitchDownOnly"] = 128] = "VehicleSubPitchDownOnly";
    Control[Control["VehicleSubThrottleUp"] = 129] = "VehicleSubThrottleUp";
    Control[Control["VehicleSubThrottleDown"] = 130] = "VehicleSubThrottleDown";
    Control[Control["VehicleSubAscend"] = 131] = "VehicleSubAscend";
    Control[Control["VehicleSubDescend"] = 132] = "VehicleSubDescend";
    Control[Control["VehicleSubTurnHardLeft"] = 133] = "VehicleSubTurnHardLeft";
    Control[Control["VehicleSubTurnHardRight"] = 134] = "VehicleSubTurnHardRight";
    Control[Control["VehicleSubMouseControlOverride"] = 135] = "VehicleSubMouseControlOverride";
    Control[Control["VehiclePushbikePedal"] = 136] = "VehiclePushbikePedal";
    Control[Control["VehiclePushbikeSprint"] = 137] = "VehiclePushbikeSprint";
    Control[Control["VehiclePushbikeFrontBrake"] = 138] = "VehiclePushbikeFrontBrake";
    Control[Control["VehiclePushbikeRearBrake"] = 139] = "VehiclePushbikeRearBrake";
    Control[Control["MeleeAttackLight"] = 140] = "MeleeAttackLight";
    Control[Control["MeleeAttackHeavy"] = 141] = "MeleeAttackHeavy";
    Control[Control["MeleeAttackAlternate"] = 142] = "MeleeAttackAlternate";
    Control[Control["MeleeBlock"] = 143] = "MeleeBlock";
    Control[Control["ParachuteDeploy"] = 144] = "ParachuteDeploy";
    Control[Control["ParachuteDetach"] = 145] = "ParachuteDetach";
    Control[Control["ParachuteTurnLeftRight"] = 146] = "ParachuteTurnLeftRight";
    Control[Control["ParachuteTurnLeftOnly"] = 147] = "ParachuteTurnLeftOnly";
    Control[Control["ParachuteTurnRightOnly"] = 148] = "ParachuteTurnRightOnly";
    Control[Control["ParachutePitchUpDown"] = 149] = "ParachutePitchUpDown";
    Control[Control["ParachutePitchUpOnly"] = 150] = "ParachutePitchUpOnly";
    Control[Control["ParachutePitchDownOnly"] = 151] = "ParachutePitchDownOnly";
    Control[Control["ParachuteBrakeLeft"] = 152] = "ParachuteBrakeLeft";
    Control[Control["ParachuteBrakeRight"] = 153] = "ParachuteBrakeRight";
    Control[Control["ParachuteSmoke"] = 154] = "ParachuteSmoke";
    Control[Control["ParachutePrecisionLanding"] = 155] = "ParachutePrecisionLanding";
    Control[Control["Map"] = 156] = "Map";
    Control[Control["SelectWeaponUnarmed"] = 157] = "SelectWeaponUnarmed";
    Control[Control["SelectWeaponMelee"] = 158] = "SelectWeaponMelee";
    Control[Control["SelectWeaponHandgun"] = 159] = "SelectWeaponHandgun";
    Control[Control["SelectWeaponShotgun"] = 160] = "SelectWeaponShotgun";
    Control[Control["SelectWeaponSmg"] = 161] = "SelectWeaponSmg";
    Control[Control["SelectWeaponAutoRifle"] = 162] = "SelectWeaponAutoRifle";
    Control[Control["SelectWeaponSniper"] = 163] = "SelectWeaponSniper";
    Control[Control["SelectWeaponHeavy"] = 164] = "SelectWeaponHeavy";
    Control[Control["SelectWeaponSpecial"] = 165] = "SelectWeaponSpecial";
    Control[Control["SelectCharacterMichael"] = 166] = "SelectCharacterMichael";
    Control[Control["SelectCharacterFranklin"] = 167] = "SelectCharacterFranklin";
    Control[Control["SelectCharacterTrevor"] = 168] = "SelectCharacterTrevor";
    Control[Control["SelectCharacterMultiplayer"] = 169] = "SelectCharacterMultiplayer";
    Control[Control["SaveReplayClip"] = 170] = "SaveReplayClip";
    Control[Control["SpecialAbilityPC"] = 171] = "SpecialAbilityPC";
    Control[Control["PhoneUp"] = 172] = "PhoneUp";
    Control[Control["PhoneDown"] = 173] = "PhoneDown";
    Control[Control["PhoneLeft"] = 174] = "PhoneLeft";
    Control[Control["PhoneRight"] = 175] = "PhoneRight";
    Control[Control["PhoneSelect"] = 176] = "PhoneSelect";
    Control[Control["PhoneCancel"] = 177] = "PhoneCancel";
    Control[Control["PhoneOption"] = 178] = "PhoneOption";
    Control[Control["PhoneExtraOption"] = 179] = "PhoneExtraOption";
    Control[Control["PhoneScrollForward"] = 180] = "PhoneScrollForward";
    Control[Control["PhoneScrollBackward"] = 181] = "PhoneScrollBackward";
    Control[Control["PhoneCameraFocusLock"] = 182] = "PhoneCameraFocusLock";
    Control[Control["PhoneCameraGrid"] = 183] = "PhoneCameraGrid";
    Control[Control["PhoneCameraSelfie"] = 184] = "PhoneCameraSelfie";
    Control[Control["PhoneCameraDOF"] = 185] = "PhoneCameraDOF";
    Control[Control["PhoneCameraExpression"] = 186] = "PhoneCameraExpression";
    Control[Control["FrontendDown"] = 187] = "FrontendDown";
    Control[Control["FrontendUp"] = 188] = "FrontendUp";
    Control[Control["FrontendLeft"] = 189] = "FrontendLeft";
    Control[Control["FrontendRight"] = 190] = "FrontendRight";
    Control[Control["FrontendRdown"] = 191] = "FrontendRdown";
    Control[Control["FrontendRup"] = 192] = "FrontendRup";
    Control[Control["FrontendRleft"] = 193] = "FrontendRleft";
    Control[Control["FrontendRright"] = 194] = "FrontendRright";
    Control[Control["FrontendAxisX"] = 195] = "FrontendAxisX";
    Control[Control["FrontendAxisY"] = 196] = "FrontendAxisY";
    Control[Control["FrontendRightAxisX"] = 197] = "FrontendRightAxisX";
    Control[Control["FrontendRightAxisY"] = 198] = "FrontendRightAxisY";
    Control[Control["FrontendPause"] = 199] = "FrontendPause";
    Control[Control["FrontendPauseAlternate"] = 200] = "FrontendPauseAlternate";
    Control[Control["FrontendAccept"] = 201] = "FrontendAccept";
    Control[Control["FrontendCancel"] = 202] = "FrontendCancel";
    Control[Control["FrontendX"] = 203] = "FrontendX";
    Control[Control["FrontendY"] = 204] = "FrontendY";
    Control[Control["FrontendLb"] = 205] = "FrontendLb";
    Control[Control["FrontendRb"] = 206] = "FrontendRb";
    Control[Control["FrontendLt"] = 207] = "FrontendLt";
    Control[Control["FrontendRt"] = 208] = "FrontendRt";
    Control[Control["FrontendLs"] = 209] = "FrontendLs";
    Control[Control["FrontendRs"] = 210] = "FrontendRs";
    Control[Control["FrontendLeaderboard"] = 211] = "FrontendLeaderboard";
    Control[Control["FrontendSocialClub"] = 212] = "FrontendSocialClub";
    Control[Control["FrontendSocialClubSecondary"] = 213] = "FrontendSocialClubSecondary";
    Control[Control["FrontendDelete"] = 214] = "FrontendDelete";
    Control[Control["FrontendEndscreenAccept"] = 215] = "FrontendEndscreenAccept";
    Control[Control["FrontendEndscreenExpand"] = 216] = "FrontendEndscreenExpand";
    Control[Control["FrontendSelect"] = 217] = "FrontendSelect";
    Control[Control["ScriptLeftAxisX"] = 218] = "ScriptLeftAxisX";
    Control[Control["ScriptLeftAxisY"] = 219] = "ScriptLeftAxisY";
    Control[Control["ScriptRightAxisX"] = 220] = "ScriptRightAxisX";
    Control[Control["ScriptRightAxisY"] = 221] = "ScriptRightAxisY";
    Control[Control["ScriptRUp"] = 222] = "ScriptRUp";
    Control[Control["ScriptRDown"] = 223] = "ScriptRDown";
    Control[Control["ScriptRLeft"] = 224] = "ScriptRLeft";
    Control[Control["ScriptRRight"] = 225] = "ScriptRRight";
    Control[Control["ScriptLB"] = 226] = "ScriptLB";
    Control[Control["ScriptRB"] = 227] = "ScriptRB";
    Control[Control["ScriptLT"] = 228] = "ScriptLT";
    Control[Control["ScriptRT"] = 229] = "ScriptRT";
    Control[Control["ScriptLS"] = 230] = "ScriptLS";
    Control[Control["ScriptRS"] = 231] = "ScriptRS";
    Control[Control["ScriptPadUp"] = 232] = "ScriptPadUp";
    Control[Control["ScriptPadDown"] = 233] = "ScriptPadDown";
    Control[Control["ScriptPadLeft"] = 234] = "ScriptPadLeft";
    Control[Control["ScriptPadRight"] = 235] = "ScriptPadRight";
    Control[Control["ScriptSelect"] = 236] = "ScriptSelect";
    Control[Control["CursorAccept"] = 237] = "CursorAccept";
    Control[Control["CursorCancel"] = 238] = "CursorCancel";
    Control[Control["CursorX"] = 239] = "CursorX";
    Control[Control["CursorY"] = 240] = "CursorY";
    Control[Control["CursorScrollUp"] = 241] = "CursorScrollUp";
    Control[Control["CursorScrollDown"] = 242] = "CursorScrollDown";
    Control[Control["EnterCheatCode"] = 243] = "EnterCheatCode";
    Control[Control["InteractionMenu"] = 244] = "InteractionMenu";
    Control[Control["MpTextChatAll"] = 245] = "MpTextChatAll";
    Control[Control["MpTextChatTeam"] = 246] = "MpTextChatTeam";
    Control[Control["MpTextChatFriends"] = 247] = "MpTextChatFriends";
    Control[Control["MpTextChatCrew"] = 248] = "MpTextChatCrew";
    Control[Control["PushToTalk"] = 249] = "PushToTalk";
    Control[Control["CreatorLS"] = 250] = "CreatorLS";
    Control[Control["CreatorRS"] = 251] = "CreatorRS";
    Control[Control["CreatorLT"] = 252] = "CreatorLT";
    Control[Control["CreatorRT"] = 253] = "CreatorRT";
    Control[Control["CreatorMenuToggle"] = 254] = "CreatorMenuToggle";
    Control[Control["CreatorAccept"] = 255] = "CreatorAccept";
    Control[Control["CreatorDelete"] = 256] = "CreatorDelete";
    Control[Control["Attack2"] = 257] = "Attack2";
    Control[Control["RappelJump"] = 258] = "RappelJump";
    Control[Control["RappelLongJump"] = 259] = "RappelLongJump";
    Control[Control["RappelSmashWindow"] = 260] = "RappelSmashWindow";
    Control[Control["PrevWeapon"] = 261] = "PrevWeapon";
    Control[Control["NextWeapon"] = 262] = "NextWeapon";
    Control[Control["MeleeAttack1"] = 263] = "MeleeAttack1";
    Control[Control["MeleeAttack2"] = 264] = "MeleeAttack2";
    Control[Control["Whistle"] = 265] = "Whistle";
    Control[Control["MoveLeft"] = 266] = "MoveLeft";
    Control[Control["MoveRight"] = 267] = "MoveRight";
    Control[Control["MoveUp"] = 268] = "MoveUp";
    Control[Control["MoveDown"] = 269] = "MoveDown";
    Control[Control["LookLeft"] = 270] = "LookLeft";
    Control[Control["LookRight"] = 271] = "LookRight";
    Control[Control["LookUp"] = 272] = "LookUp";
    Control[Control["LookDown"] = 273] = "LookDown";
    Control[Control["SniperZoomIn"] = 274] = "SniperZoomIn";
    Control[Control["SniperZoomOut"] = 275] = "SniperZoomOut";
    Control[Control["SniperZoomInAlternate"] = 276] = "SniperZoomInAlternate";
    Control[Control["SniperZoomOutAlternate"] = 277] = "SniperZoomOutAlternate";
    Control[Control["VehicleMoveLeft"] = 278] = "VehicleMoveLeft";
    Control[Control["VehicleMoveRight"] = 279] = "VehicleMoveRight";
    Control[Control["VehicleMoveUp"] = 280] = "VehicleMoveUp";
    Control[Control["VehicleMoveDown"] = 281] = "VehicleMoveDown";
    Control[Control["VehicleGunLeft"] = 282] = "VehicleGunLeft";
    Control[Control["VehicleGunRight"] = 283] = "VehicleGunRight";
    Control[Control["VehicleGunUp"] = 284] = "VehicleGunUp";
    Control[Control["VehicleGunDown"] = 285] = "VehicleGunDown";
    Control[Control["VehicleLookLeft"] = 286] = "VehicleLookLeft";
    Control[Control["VehicleLookRight"] = 287] = "VehicleLookRight";
    Control[Control["ReplayStartStopRecording"] = 288] = "ReplayStartStopRecording";
    Control[Control["ReplayStartStopRecordingSecondary"] = 289] = "ReplayStartStopRecordingSecondary";
    Control[Control["ScaledLookLeftRight"] = 290] = "ScaledLookLeftRight";
    Control[Control["ScaledLookUpDown"] = 291] = "ScaledLookUpDown";
    Control[Control["ScaledLookUpOnly"] = 292] = "ScaledLookUpOnly";
    Control[Control["ScaledLookDownOnly"] = 293] = "ScaledLookDownOnly";
    Control[Control["ScaledLookLeftOnly"] = 294] = "ScaledLookLeftOnly";
    Control[Control["ScaledLookRightOnly"] = 295] = "ScaledLookRightOnly";
    Control[Control["ReplayMarkerDelete"] = 296] = "ReplayMarkerDelete";
    Control[Control["ReplayClipDelete"] = 297] = "ReplayClipDelete";
    Control[Control["ReplayPause"] = 298] = "ReplayPause";
    Control[Control["ReplayRewind"] = 299] = "ReplayRewind";
    Control[Control["ReplayFfwd"] = 300] = "ReplayFfwd";
    Control[Control["ReplayNewmarker"] = 301] = "ReplayNewmarker";
    Control[Control["ReplayRecord"] = 302] = "ReplayRecord";
    Control[Control["ReplayScreenshot"] = 303] = "ReplayScreenshot";
    Control[Control["ReplayHidehud"] = 304] = "ReplayHidehud";
    Control[Control["ReplayStartpoint"] = 305] = "ReplayStartpoint";
    Control[Control["ReplayEndpoint"] = 306] = "ReplayEndpoint";
    Control[Control["ReplayAdvance"] = 307] = "ReplayAdvance";
    Control[Control["ReplayBack"] = 308] = "ReplayBack";
    Control[Control["ReplayTools"] = 309] = "ReplayTools";
    Control[Control["ReplayRestart"] = 310] = "ReplayRestart";
    Control[Control["ReplayShowhotkey"] = 311] = "ReplayShowhotkey";
    Control[Control["ReplayCycleMarkerLeft"] = 312] = "ReplayCycleMarkerLeft";
    Control[Control["ReplayCycleMarkerRight"] = 313] = "ReplayCycleMarkerRight";
    Control[Control["ReplayFOVIncrease"] = 314] = "ReplayFOVIncrease";
    Control[Control["ReplayFOVDecrease"] = 315] = "ReplayFOVDecrease";
    Control[Control["ReplayCameraUp"] = 316] = "ReplayCameraUp";
    Control[Control["ReplayCameraDown"] = 317] = "ReplayCameraDown";
    Control[Control["ReplaySave"] = 318] = "ReplaySave";
    Control[Control["ReplayToggletime"] = 319] = "ReplayToggletime";
    Control[Control["ReplayToggletips"] = 320] = "ReplayToggletips";
    Control[Control["ReplayPreview"] = 321] = "ReplayPreview";
    Control[Control["ReplayToggleTimeline"] = 322] = "ReplayToggleTimeline";
    Control[Control["ReplayTimelinePickupClip"] = 323] = "ReplayTimelinePickupClip";
    Control[Control["ReplayTimelineDuplicateClip"] = 324] = "ReplayTimelineDuplicateClip";
    Control[Control["ReplayTimelinePlaceClip"] = 325] = "ReplayTimelinePlaceClip";
    Control[Control["ReplayCtrl"] = 326] = "ReplayCtrl";
    Control[Control["ReplayTimelineSave"] = 327] = "ReplayTimelineSave";
    Control[Control["ReplayPreviewAudio"] = 328] = "ReplayPreviewAudio";
    Control[Control["VehicleDriveLook"] = 329] = "VehicleDriveLook";
    Control[Control["VehicleDriveLook2"] = 330] = "VehicleDriveLook2";
    Control[Control["VehicleFlyAttack2"] = 331] = "VehicleFlyAttack2";
    Control[Control["RadioWheelUpDown"] = 332] = "RadioWheelUpDown";
    Control[Control["RadioWheelLeftRight"] = 333] = "RadioWheelLeftRight";
    Control[Control["VehicleSlowMoUpDown"] = 334] = "VehicleSlowMoUpDown";
    Control[Control["VehicleSlowMoUpOnly"] = 335] = "VehicleSlowMoUpOnly";
    Control[Control["VehicleSlowMoDownOnly"] = 336] = "VehicleSlowMoDownOnly";
    Control[Control["VehicleHydraulicsControlToggle"] = 337] = "VehicleHydraulicsControlToggle";
    Control[Control["VehicleHydraulicsControlLeft"] = 338] = "VehicleHydraulicsControlLeft";
    Control[Control["VehicleHydraulicsControlRight"] = 339] = "VehicleHydraulicsControlRight";
    Control[Control["VehicleHydraulicsControlUp"] = 340] = "VehicleHydraulicsControlUp";
    Control[Control["VehicleHydraulicsControlDown"] = 341] = "VehicleHydraulicsControlDown";
    Control[Control["VehicleHydraulicsControlUpDown"] = 342] = "VehicleHydraulicsControlUpDown";
    Control[Control["VehicleHydraulicsControlLeftRight"] = 343] = "VehicleHydraulicsControlLeftRight";
    Control[Control["SwitchVisor"] = 344] = "SwitchVisor";
    Control[Control["VehicleMeleeHold"] = 345] = "VehicleMeleeHold";
    Control[Control["VehicleMeleeLeft"] = 346] = "VehicleMeleeLeft";
    Control[Control["VehicleMeleeRight"] = 347] = "VehicleMeleeRight";
    Control[Control["MapPointOfInterest"] = 348] = "MapPointOfInterest";
    Control[Control["ReplaySnapmaticPhoto"] = 349] = "ReplaySnapmaticPhoto";
    Control[Control["VehicleCarJump"] = 350] = "VehicleCarJump";
    Control[Control["VehicleRocketBoost"] = 351] = "VehicleRocketBoost";
    Control[Control["VehicleFlyBoost"] = 352] = "VehicleFlyBoost";
    Control[Control["VehicleParachute"] = 353] = "VehicleParachute";
    Control[Control["VehicleBikeWings"] = 354] = "VehicleBikeWings";
    Control[Control["VehicleFlyBombBay"] = 355] = "VehicleFlyBombBay";
    Control[Control["VehicleFlyCounter"] = 356] = "VehicleFlyCounter";
    Control[Control["VehicleFlyTransform"] = 357] = "VehicleFlyTransform";
})(Control || (Control = {}));
