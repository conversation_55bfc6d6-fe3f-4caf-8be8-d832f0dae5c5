import { DELAY } from "functions";

/**
 * Defines the structure for a spawn point.
 */
interface SpawnPoint {
    x: number;
    y: number;
    z: number;
    heading: number;
    model: string | number;
    idx?: number;
    skipFade?: boolean;
}

// In-memory spawnpoint array
const spawnPoints: SpawnPoint[] = [];

// Auto-spawn state
let autoSpawnEnabled = false;
let autoSpawnCallback: Function | null = null;

// Spawn state
let spawnLock = false;
let respawnForced = false;
let diedAt: number | null = null;

// Counter for unique spawn point indices
let spawnNum = 1;

/**
 * --- MapManager Integration ---
 * Listens for map directives to dynamically add/remove spawn points.
 */
on('getMapDirectives', (add: (directive: string, addCb: Function, removeCb: Function) => void) => {
    add('spawnpoint', (state: any, model: string) => {
        return (opts: any) => {
            try {
                const x = (opts.x || opts[0]) + 0.0001;
                const y = (opts.y || opts[1]) + 0.0001;
                const z = (opts.z || opts[2]) + 0.0001;
                const heading = (opts.heading || 0) + 0.01;

                const modelHash = typeof model === 'string' ? GetHashKey(model) : model;

                const spawnPoint = { x, y, z, heading, model: modelHash };
                const spawnIdx = addSpawnPoint(spawnPoint);

                // Store data in the state for later removal by mapmanager
                if (spawnIdx) {
                    state.add('spawnIdx', spawnIdx);
                }
            } catch (e) {
                console.error(`[spawnmanager] Error processing spawnpoint directive: ${e}`);
            }
        };
    }, (state: any) => {
        if (state.spawnIdx) {
            removeSpawnPoint(state.spawnIdx);
        }
    });
});

/**
 * Loads a set of spawn points from a JSON string.
 * @param spawnString The JSON string containing spawn points.
 */
export function loadSpawns(spawnString: string): void {
    try {
        const data = JSON.parse(spawnString);
        if (!data.spawns || !Array.isArray(data.spawns)) {
            console.error("[spawnmanager] Invalid spawn data: 'spawns' array not found in JSON.");
            return;
        }

        data.spawns.forEach(addSpawnPoint);
    } catch (e) {
        console.error(`[spawnmanager] Failed to load spawns from JSON: ${e}`);
    }
}

/**
 * Adds a single spawn point to the list.
 * @param spawn The spawn point object.
 * @returns The index of the added spawn point.
 */
export function addSpawnPoint(spawn: SpawnPoint): number | void {
    try {
        // Validate position and heading
        if (typeof spawn.x !== 'number' || typeof spawn.y !== 'number' || typeof spawn.z !== 'number' || typeof spawn.heading !== 'number') {
            throw new Error("Invalid spawn data: x, y, z, and heading must be numbers.");
        }

        // Validate model
        let model: string | number = spawn.model;
        if (typeof model === 'string') {
            model = GetHashKey(model);
        }

        if (!IsModelInCdimage(model)) {
            throw new Error(`Invalid spawn model: ${spawn.model}`);
        }

        spawn.model = model;
        spawn.idx = spawnNum++;
        
        spawnPoints.push(spawn);
        return spawn.idx;
    } catch (e) {
        console.error(`[spawnmanager] Could not add spawn point: ${e}`);
    }
}

/**
 * Removes a spawn point by its unique index.
 * @param spawnIdx The index of the spawn point to remove.
 */
export function removeSpawnPoint(spawnIdx: number): void {
    const index = spawnPoints.findIndex(sp => sp.idx === spawnIdx);
    if (index > -1) {
        spawnPoints.splice(index, 1);
    }
}

/**
 * Enables or disables automatic spawning.
 * @param enabled True to enable, false to disable.
 */
export function setAutoSpawn(enabled: boolean): void {
    autoSpawnEnabled = enabled;
}

/**
 * Sets a callback to execute instead of the default spawn logic when auto-spawning.
 * @param cb The callback function.
 */
export function setAutoSpawnCallback(cb: Function | null): void {
    autoSpawnCallback = cb;
    autoSpawnEnabled = (cb !== null);
}

/**
 * Freezes or unfreezes the player.
 * @param freeze True to freeze, false to unfreeze.
 */
export function freezePlayer(freeze: boolean): void {
    const player = PlayerId();
    SetPlayerControl(player, !freeze, 0);

    const ped = PlayerPedId();

    if (!freeze) {
        if (!IsEntityVisible(ped)) SetEntityVisible(ped, true, false);
        if (!IsPedInAnyVehicle(ped, false)) SetEntityCollision(ped, true, true);
        FreezeEntityPosition(ped, false);
        SetPlayerInvincible(player, false);
    } else {
        if (IsEntityVisible(ped)) SetEntityVisible(ped, false, false);
        SetEntityCollision(ped, false, true);
        FreezeEntityPosition(ped, true);
        SetPlayerInvincible(player, true);

        if (!IsPedFatallyInjured(ped)) {
            ClearPedTasksImmediately(ped);
        }
    }
}

/**
 * Spawns the current player at a given spawn point.
 * @param spawnIdentifier Optional spawn point index or a spawn point object. If not provided, a random one is chosen.
 * @param cb Optional callback to run after spawning.
 */
export async function spawnPlayer(spawnIdentifier?: number | SpawnPoint, cb?: (spawn: SpawnPoint) => void): Promise<void> {
    if (spawnLock) return;
    spawnLock = true;

    try {
        let spawn: SpawnPoint | undefined;

        if (typeof spawnIdentifier === 'object') {
            spawn = { ...spawnIdentifier };
        } else if (typeof spawnIdentifier === 'number') {
            spawn = spawnPoints.find(sp => sp.idx === spawnIdentifier);
        } else {
            if (spawnPoints.length === 0) throw new Error("No spawn points available.");
            const randomIndex = GetRandomIntInRange(0, spawnPoints.length - 1);
            spawn = spawnPoints[randomIndex];
        }

        if (!spawn) throw new Error("Tried to spawn at an invalid spawn index or object.");

        if (!spawn.skipFade) {
            DoScreenFadeOut(500);
            await DELAY(500);
        }

        freezePlayer(true);

        const model = spawn.model;
        RequestModel(model);
        while (!HasModelLoaded(model)) await DELAY(0);

        SetPlayerModel(PlayerId(), model);
        SetModelAsNoLongerNeeded(model);

        const ped = PlayerPedId();
        RequestCollisionAtCoord(spawn.x, spawn.y, spawn.z);
        SetEntityCoordsNoOffset(ped, spawn.x, spawn.y, spawn.z, true, true, true);
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, 10, false);

        ClearPedTasksImmediately(ped);
        RemoveAllPedWeapons(ped, true);
        ClearPlayerWantedLevel(PlayerId());

        const startTime = GetGameTimer();
        while (!HasCollisionLoadedAroundEntity(ped) && (GetGameTimer() - startTime) < 5000) {
            await DELAY(0);
        }

        ShutdownLoadingScreen();
        ShutdownLoadingScreenNui();

        if (IsScreenFadedOut()) {
            DoScreenFadeIn(500);
            await DELAY(500);
        }

        freezePlayer(false);

        TriggerEvent('playerSpawned', spawn);
        cb?.(spawn);

    } catch (e) {
        console.error(`[spawnmanager] Error in spawnPlayer: ${e}`);
    } finally {
        spawnLock = false;
    }
}

/**
 * Forces an immediate respawn on the next tick.
 */
export function forceRespawn(): void {
    spawnLock = false;
    respawnForced = true;
}

// --- Exports ---
// exports('spawnPlayer', spawnPlayer);
// exports('addSpawnPoint', addSpawnPoint);
// exports('removeSpawnPoint', removeSpawnPoint);
// exports('loadSpawns', loadSpawns);
// exports('setAutoSpawn', setAutoSpawn);
// exports('setAutoSpawnCallback', setAutoSpawnCallback);
// exports('forceRespawn', forceRespawn);
