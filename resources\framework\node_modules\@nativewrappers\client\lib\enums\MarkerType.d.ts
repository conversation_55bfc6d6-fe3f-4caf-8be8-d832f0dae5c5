/**
 * List of markers. Markers are 3D visual objects in the world.
 *
 * See native [DRAW_MARKER](https://docs.fivem.net/game-references/markers/) for pictures.
 */
export declare enum MarkerType {
    UpsideDownCone = 0,
    VerticalCylinder = 1,
    ThickChevronUp = 2,
    ThinChevronUp = 3,
    CheckeredFlagRect = 4,
    CheckeredFlagCircle = 5,
    VerticleCircle = 6,
    PlaneModel = 7,
    LostMCDark = 8,
    LostMCLight = 9,
    Number0 = 10,
    Number1 = 11,
    Number2 = 12,
    Number3 = 13,
    Number4 = 14,
    Number5 = 15,
    Number6 = 16,
    Number7 = 17,
    Number8 = 18,
    Number9 = 19,
    ChevronUpx1 = 20,
    ChevronUpx2 = 21,
    ChevronUpx3 = 22,
    HorizontalCircleFat = 23,
    ReplayIcon = 24,
    HorizontalCircleSkinny = 25,
    HorizontalCircleSkinnyArrow = 26,
    HorizontalSplitArrowCircle = 27,
    DebugSphere = 28,
    DollarSign = 29,
    HorizontalBars = 30,
    WolfHead = 31,
    QuestionMark = 32,
    PlaneSymbol = 33,
    HelicopterSymbol = 34,
    BoatSymbol = 35,
    CarSymbol = 36,
    MotorcycleSymbol = 37,
    BikeSymbol = 38,
    TruckSymbol = 39,
    ParachuteSymbol = 40,
    SawbladeSymbol = 41
}
