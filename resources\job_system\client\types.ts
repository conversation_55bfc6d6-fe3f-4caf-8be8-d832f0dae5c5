export interface Job {
  id: string;
  name: string;
  category: string;
  summary: string;
  narrative_flavor: string;
  prerequisites: Prerequisites;
  start: Start;
  flow: Flow;
  mechanics: Mechanics;
  locations: Location[];
  npcs: Npc[];
  props_vehicles: PropsVehicles;
  rewards: Rewards;
  balance: Balance;
  configuration: Configuration;
  events: Events;
  dependencies: Dependencies;
  ui_text: UiText;
  localization_keys: string[];
  anti_exploit: string[];
  test_scenarios: string[];
  extensibility: string[];
  version: string;
}

export interface Prerequisites {
  level: number;
  licenses: string[];
  items_required: string[];
}

export interface Start {
  how_to_start: string;
  start_locations: Location[];
}

export interface Location {
  label: string;
  coords: Coords;
  radius: number;
}

export interface Coords {
  x: number;
  y: number;
  z: number;
  h?: number;
}

export interface Flow {
  steps: Step[];
  average_duration_min: number;
}

export interface Step {
  order: number;
  label: string;
  description: string;
  success_criteria: string;
}

export interface Mechanics {
  interactions: string[];
  items: Items;
  timers: Timers;
  random_events: RandomEvent[];
  police_alerts: PoliceAlerts;
  cooldowns: Cooldowns;
  failure_conditions: string[];
}

export interface Items {
  consumed: string[];
  produced: string[];
}

export interface Timers {
  per_step_sec: number;
  overall_limit_min: number;
}

export interface RandomEvent {
  event: string;
  chance: number;
  effect: string;
}

export interface PoliceAlerts {
  enabled: boolean;
  conditions: string;
}

export interface Cooldowns {
  per_player_min: number;
}

export interface Npc {
  label: string;
  model: string;
  role: string;
  behavior: string;
}

export interface PropsVehicles {
  vehicles: Vehicle[];
  props: Prop[];
}

export interface Vehicle {
  model: string;
  required: boolean;
  spawn_rules: string;
}

export interface Prop {
  model: string;
  usage: string;
}

export interface Rewards {
  base_pay: number;
  per_task_bonus: number;
  streak_multiplier_max: number;
  penalties: Penalty[];
}

export interface Penalty {
  reason: string;
  amount: number;
}

export interface Balance {
  difficulty: number;
  risk_vs_reward: string;
  expected_profit_min: number;
  expected_profit_max: number;
}

export interface Configuration {
  economy_multiplier: number;
  police_required: boolean;
  framework: string;
}

export interface Events {
  client: string[];
  server: string[];
}

export interface Dependencies {
  optional: string[];
  notes: string;
}

export interface UiText {
  notifications: string[];
  helptext: string[];
  progress_labels: string[];
}
