export var BadgeStyle;
(function (BadgeStyle) {
    BadgeStyle[BadgeStyle["None"] = 0] = "None";
    BadgeStyle[BadgeStyle["Lock"] = 1] = "Lock";
    BadgeStyle[BadgeStyle["Star"] = 2] = "Star";
    BadgeStyle[BadgeStyle["Warning"] = 3] = "Warning";
    BadgeStyle[BadgeStyle["Crown"] = 4] = "Crown";
    BadgeStyle[BadgeStyle["MedalBronze"] = 5] = "MedalBronze";
    BadgeStyle[BadgeStyle["MedalGold"] = 6] = "MedalGold";
    BadgeStyle[BadgeStyle["MedalSilver"] = 7] = "MedalSilver";
    BadgeStyle[BadgeStyle["Cash"] = 8] = "Cash";
    BadgeStyle[BadgeStyle["Coke"] = 9] = "Coke";
    BadgeStyle[BadgeStyle["Heroin"] = 10] = "Heroin";
    BadgeStyle[BadgeStyle["Meth"] = 11] = "Meth";
    BadgeStyle[BadgeStyle["Weed"] = 12] = "Weed";
    BadgeStyle[BadgeStyle["Ammo"] = 13] = "Ammo";
    BadgeStyle[BadgeStyle["Armor"] = 14] = "Armor";
    BadgeStyle[BadgeStyle["Barber"] = 15] = "Barber";
    BadgeStyle[BadgeStyle["Clothing"] = 16] = "Clothing";
    BadgeStyle[BadgeStyle["Franklin"] = 17] = "Franklin";
    BadgeStyle[BadgeStyle["Bike"] = 18] = "Bike";
    BadgeStyle[BadgeStyle["Car"] = 19] = "Car";
    BadgeStyle[BadgeStyle["Gun"] = 20] = "Gun";
    BadgeStyle[BadgeStyle["HealthHeart"] = 21] = "HealthHeart";
    BadgeStyle[BadgeStyle["MakeupBrush"] = 22] = "MakeupBrush";
    BadgeStyle[BadgeStyle["Mask"] = 23] = "Mask";
    BadgeStyle[BadgeStyle["Michael"] = 24] = "Michael";
    BadgeStyle[BadgeStyle["Tattoo"] = 25] = "Tattoo";
    BadgeStyle[BadgeStyle["Tick"] = 26] = "Tick";
    BadgeStyle[BadgeStyle["Trevor"] = 27] = "Trevor";
    BadgeStyle[BadgeStyle["Female"] = 28] = "Female";
    BadgeStyle[BadgeStyle["Male"] = 29] = "Male";
    BadgeStyle[BadgeStyle["LockArena"] = 30] = "LockArena";
    BadgeStyle[BadgeStyle["Adversary"] = 31] = "Adversary";
    BadgeStyle[BadgeStyle["BaseJumping"] = 32] = "BaseJumping";
    BadgeStyle[BadgeStyle["Briefcase"] = 33] = "Briefcase";
    BadgeStyle[BadgeStyle["MissionStar"] = 34] = "MissionStar";
    BadgeStyle[BadgeStyle["Deathmatch"] = 35] = "Deathmatch";
    BadgeStyle[BadgeStyle["Castle"] = 36] = "Castle";
    BadgeStyle[BadgeStyle["Trophy"] = 37] = "Trophy";
    BadgeStyle[BadgeStyle["RaceFlag"] = 38] = "RaceFlag";
    BadgeStyle[BadgeStyle["RaceFlagPlane"] = 39] = "RaceFlagPlane";
    BadgeStyle[BadgeStyle["RaceFlagBicycle"] = 40] = "RaceFlagBicycle";
    BadgeStyle[BadgeStyle["RaceFlagPerson"] = 41] = "RaceFlagPerson";
    BadgeStyle[BadgeStyle["RaceFlagCar"] = 42] = "RaceFlagCar";
    BadgeStyle[BadgeStyle["RaceFlagBoatAnchor"] = 43] = "RaceFlagBoatAnchor";
    BadgeStyle[BadgeStyle["Rockstar"] = 44] = "Rockstar";
    BadgeStyle[BadgeStyle["Stunt"] = 45] = "Stunt";
    BadgeStyle[BadgeStyle["StuntPremium"] = 46] = "StuntPremium";
    BadgeStyle[BadgeStyle["RaceFlagStuntJump"] = 47] = "RaceFlagStuntJump";
    BadgeStyle[BadgeStyle["Shield"] = 48] = "Shield";
    BadgeStyle[BadgeStyle["TeamDeathmatch"] = 49] = "TeamDeathmatch";
    BadgeStyle[BadgeStyle["VehicleDeathmatch"] = 50] = "VehicleDeathmatch";
    BadgeStyle[BadgeStyle["MpAmmoPickup"] = 51] = "MpAmmoPickup";
    BadgeStyle[BadgeStyle["MpAmmo"] = 52] = "MpAmmo";
    BadgeStyle[BadgeStyle["MpCash"] = 53] = "MpCash";
    BadgeStyle[BadgeStyle["MpRp"] = 54] = "MpRp";
    BadgeStyle[BadgeStyle["MpSpectating"] = 55] = "MpSpectating";
    BadgeStyle[BadgeStyle["Sale"] = 56] = "Sale";
    BadgeStyle[BadgeStyle["GlobeWhite"] = 57] = "GlobeWhite";
    BadgeStyle[BadgeStyle["GlobeRed"] = 58] = "GlobeRed";
    BadgeStyle[BadgeStyle["GlobeBlue"] = 59] = "GlobeBlue";
    BadgeStyle[BadgeStyle["GlobeYellow"] = 60] = "GlobeYellow";
    BadgeStyle[BadgeStyle["GlobeGreen"] = 61] = "GlobeGreen";
    BadgeStyle[BadgeStyle["GlobeOrange"] = 62] = "GlobeOrange";
    BadgeStyle[BadgeStyle["InvArmWrestling"] = 63] = "InvArmWrestling";
    BadgeStyle[BadgeStyle["InvBasejump"] = 64] = "InvBasejump";
    BadgeStyle[BadgeStyle["InvMission"] = 65] = "InvMission";
    BadgeStyle[BadgeStyle["InvDarts"] = 66] = "InvDarts";
    BadgeStyle[BadgeStyle["InvDeathmatch"] = 67] = "InvDeathmatch";
    BadgeStyle[BadgeStyle["InvDrug"] = 68] = "InvDrug";
    BadgeStyle[BadgeStyle["InvCastle"] = 69] = "InvCastle";
    BadgeStyle[BadgeStyle["InvGolf"] = 70] = "InvGolf";
    BadgeStyle[BadgeStyle["InvBike"] = 71] = "InvBike";
    BadgeStyle[BadgeStyle["InvBoat"] = 72] = "InvBoat";
    BadgeStyle[BadgeStyle["InvAnchor"] = 73] = "InvAnchor";
    BadgeStyle[BadgeStyle["InvCar"] = 74] = "InvCar";
    BadgeStyle[BadgeStyle["InvDollar"] = 75] = "InvDollar";
    BadgeStyle[BadgeStyle["InvCoke"] = 76] = "InvCoke";
    BadgeStyle[BadgeStyle["InvKey"] = 77] = "InvKey";
    BadgeStyle[BadgeStyle["InvData"] = 78] = "InvData";
    BadgeStyle[BadgeStyle["InvHeli"] = 79] = "InvHeli";
    BadgeStyle[BadgeStyle["InvHeorin"] = 80] = "InvHeorin";
    BadgeStyle[BadgeStyle["InvKeycard"] = 81] = "InvKeycard";
    BadgeStyle[BadgeStyle["InvMeth"] = 82] = "InvMeth";
    BadgeStyle[BadgeStyle["InvBriefcase"] = 83] = "InvBriefcase";
    BadgeStyle[BadgeStyle["InvLink"] = 84] = "InvLink";
    BadgeStyle[BadgeStyle["InvPerson"] = 85] = "InvPerson";
    BadgeStyle[BadgeStyle["InvPlane"] = 86] = "InvPlane";
    BadgeStyle[BadgeStyle["InvPlane2"] = 87] = "InvPlane2";
    BadgeStyle[BadgeStyle["InvQuestionmark"] = 88] = "InvQuestionmark";
    BadgeStyle[BadgeStyle["InvRemote"] = 89] = "InvRemote";
    BadgeStyle[BadgeStyle["InvSafe"] = 90] = "InvSafe";
    BadgeStyle[BadgeStyle["InvSteerWheel"] = 91] = "InvSteerWheel";
    BadgeStyle[BadgeStyle["InvWeapon"] = 92] = "InvWeapon";
    BadgeStyle[BadgeStyle["InvWeed"] = 93] = "InvWeed";
    BadgeStyle[BadgeStyle["InvRaceFlagPlane"] = 94] = "InvRaceFlagPlane";
    BadgeStyle[BadgeStyle["InvRaceFlagBicycle"] = 95] = "InvRaceFlagBicycle";
    BadgeStyle[BadgeStyle["InvRaceFlagBoatAnchor"] = 96] = "InvRaceFlagBoatAnchor";
    BadgeStyle[BadgeStyle["InvRaceFlagPerson"] = 97] = "InvRaceFlagPerson";
    BadgeStyle[BadgeStyle["InvRaceFlagCar"] = 98] = "InvRaceFlagCar";
    BadgeStyle[BadgeStyle["InvRaceFlagHelmet"] = 99] = "InvRaceFlagHelmet";
    BadgeStyle[BadgeStyle["InvShootingRange"] = 100] = "InvShootingRange";
    BadgeStyle[BadgeStyle["InvSurvival"] = 101] = "InvSurvival";
    BadgeStyle[BadgeStyle["InvTeamDeathmatch"] = 102] = "InvTeamDeathmatch";
    BadgeStyle[BadgeStyle["InvTennis"] = 103] = "InvTennis";
    BadgeStyle[BadgeStyle["InvVehicleDeathmatch"] = 104] = "InvVehicleDeathmatch";
    BadgeStyle[BadgeStyle["AudioMute"] = 105] = "AudioMute";
    BadgeStyle[BadgeStyle["AudioInactive"] = 106] = "AudioInactive";
    BadgeStyle[BadgeStyle["AudioVol1"] = 107] = "AudioVol1";
    BadgeStyle[BadgeStyle["AudioVol2"] = 108] = "AudioVol2";
    BadgeStyle[BadgeStyle["AudioVol3"] = 109] = "AudioVol3";
    BadgeStyle[BadgeStyle["CountryUsa"] = 110] = "CountryUsa";
    BadgeStyle[BadgeStyle["CountryUk"] = 111] = "CountryUk";
    BadgeStyle[BadgeStyle["CountrySweden"] = 112] = "CountrySweden";
    BadgeStyle[BadgeStyle["CountryKorea"] = 113] = "CountryKorea";
    BadgeStyle[BadgeStyle["CountryJapan"] = 114] = "CountryJapan";
    BadgeStyle[BadgeStyle["CountryItaly"] = 115] = "CountryItaly";
    BadgeStyle[BadgeStyle["CountryGermany"] = 116] = "CountryGermany";
    BadgeStyle[BadgeStyle["CountryFrance"] = 117] = "CountryFrance";
    BadgeStyle[BadgeStyle["BrandAlbany"] = 118] = "BrandAlbany";
    BadgeStyle[BadgeStyle["BrandAnnis"] = 119] = "BrandAnnis";
    BadgeStyle[BadgeStyle["BrandBanshee"] = 120] = "BrandBanshee";
    BadgeStyle[BadgeStyle["BrandBenefactor"] = 121] = "BrandBenefactor";
    BadgeStyle[BadgeStyle["BrandBf"] = 122] = "BrandBf";
    BadgeStyle[BadgeStyle["BrandBollokan"] = 123] = "BrandBollokan";
    BadgeStyle[BadgeStyle["BrandBravado"] = 124] = "BrandBravado";
    BadgeStyle[BadgeStyle["BrandBrute"] = 125] = "BrandBrute";
    BadgeStyle[BadgeStyle["BrandBuckingham"] = 126] = "BrandBuckingham";
    BadgeStyle[BadgeStyle["BrandCanis"] = 127] = "BrandCanis";
    BadgeStyle[BadgeStyle["BrandChariot"] = 128] = "BrandChariot";
    BadgeStyle[BadgeStyle["BrandCheval"] = 129] = "BrandCheval";
    BadgeStyle[BadgeStyle["BrandClassique"] = 130] = "BrandClassique";
    BadgeStyle[BadgeStyle["BrandCoil"] = 131] = "BrandCoil";
    BadgeStyle[BadgeStyle["BrandDeclasse"] = 132] = "BrandDeclasse";
    BadgeStyle[BadgeStyle["BrandDewbauchee"] = 133] = "BrandDewbauchee";
    BadgeStyle[BadgeStyle["BrandDilettante"] = 134] = "BrandDilettante";
    BadgeStyle[BadgeStyle["BrandDinka"] = 135] = "BrandDinka";
    BadgeStyle[BadgeStyle["BrandDundreary"] = 136] = "BrandDundreary";
    BadgeStyle[BadgeStyle["BrandEmporer"] = 137] = "BrandEmporer";
    BadgeStyle[BadgeStyle["BrandEnus"] = 138] = "BrandEnus";
    BadgeStyle[BadgeStyle["BrandFathom"] = 139] = "BrandFathom";
    BadgeStyle[BadgeStyle["BrandGalivanter"] = 140] = "BrandGalivanter";
    BadgeStyle[BadgeStyle["BrandGrotti"] = 141] = "BrandGrotti";
    BadgeStyle[BadgeStyle["BrandGrotti2"] = 142] = "BrandGrotti2";
    BadgeStyle[BadgeStyle["BrandHijak"] = 143] = "BrandHijak";
    BadgeStyle[BadgeStyle["BrandHvy"] = 144] = "BrandHvy";
    BadgeStyle[BadgeStyle["BrandImponte"] = 145] = "BrandImponte";
    BadgeStyle[BadgeStyle["BrandInvetero"] = 146] = "BrandInvetero";
    BadgeStyle[BadgeStyle["BrandJacksheepe"] = 147] = "BrandJacksheepe";
    BadgeStyle[BadgeStyle["BrandLcc"] = 148] = "BrandLcc";
    BadgeStyle[BadgeStyle["BrandJobuilt"] = 149] = "BrandJobuilt";
    BadgeStyle[BadgeStyle["BrandKarin"] = 150] = "BrandKarin";
    BadgeStyle[BadgeStyle["BrandLampadati"] = 151] = "BrandLampadati";
    BadgeStyle[BadgeStyle["BrandMaibatsu"] = 152] = "BrandMaibatsu";
    BadgeStyle[BadgeStyle["BrandMammoth"] = 153] = "BrandMammoth";
    BadgeStyle[BadgeStyle["BrandMtl"] = 154] = "BrandMtl";
    BadgeStyle[BadgeStyle["BrandNagasaki"] = 155] = "BrandNagasaki";
    BadgeStyle[BadgeStyle["BrandObey"] = 156] = "BrandObey";
    BadgeStyle[BadgeStyle["BrandOcelot"] = 157] = "BrandOcelot";
    BadgeStyle[BadgeStyle["BrandOverflod"] = 158] = "BrandOverflod";
    BadgeStyle[BadgeStyle["BrandPed"] = 159] = "BrandPed";
    BadgeStyle[BadgeStyle["BrandPegassi"] = 160] = "BrandPegassi";
    BadgeStyle[BadgeStyle["BrandPfister"] = 161] = "BrandPfister";
    BadgeStyle[BadgeStyle["BrandPrincipe"] = 162] = "BrandPrincipe";
    BadgeStyle[BadgeStyle["BrandProgen"] = 163] = "BrandProgen";
    BadgeStyle[BadgeStyle["BrandProgen2"] = 164] = "BrandProgen2";
    BadgeStyle[BadgeStyle["BrandRune"] = 165] = "BrandRune";
    BadgeStyle[BadgeStyle["BrandSchyster"] = 166] = "BrandSchyster";
    BadgeStyle[BadgeStyle["BrandShitzu"] = 167] = "BrandShitzu";
    BadgeStyle[BadgeStyle["BrandSpeedophile"] = 168] = "BrandSpeedophile";
    BadgeStyle[BadgeStyle["BrandStanley"] = 169] = "BrandStanley";
    BadgeStyle[BadgeStyle["BrandTruffade"] = 170] = "BrandTruffade";
    BadgeStyle[BadgeStyle["BrandUbermacht"] = 171] = "BrandUbermacht";
    BadgeStyle[BadgeStyle["BrandVapid"] = 172] = "BrandVapid";
    BadgeStyle[BadgeStyle["BrandVulcar"] = 173] = "BrandVulcar";
    BadgeStyle[BadgeStyle["BrandWeeny"] = 174] = "BrandWeeny";
    BadgeStyle[BadgeStyle["BrandWestern"] = 175] = "BrandWestern";
    BadgeStyle[BadgeStyle["BrandWesternmotorcycle"] = 176] = "BrandWesternmotorcycle";
    BadgeStyle[BadgeStyle["BrandWillard"] = 177] = "BrandWillard";
    BadgeStyle[BadgeStyle["BrandZirconium"] = 178] = "BrandZirconium";
    BadgeStyle[BadgeStyle["Info"] = 179] = "Info";
})(BadgeStyle || (BadgeStyle = {}));
