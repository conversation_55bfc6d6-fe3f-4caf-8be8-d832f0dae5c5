import { Job } from 'types';
import fs from 'fs';
import path from 'path';
import { setTimeout } from 'timers/promises';


onNet('ollama_ped:gather_labor_job', async () => {
  const src = (global as any).source;
  const JOBS_DIRECTORY = 'resources/ollama_ped/server/jobs'; // Adjust path as needed
  const ALL_JOBS = loadJobs(JOBS_DIRECTORY);
  const RANDOM_SELECT = ALL_JOBS[Math.floor(Math.random() * ALL_JOBS.length)];
  await setTimeout(3000, 'result');
  emitNet('ollama_ped:job_reply', src, JSON.stringify(RANDOM_SELECT));
})


// Function to load all JSON files from the directory
function loadJobs(directory: string): Job[] {
  const jobs: Job[] = [];
  const files = fs.readdirSync(directory);

  for (const file of files) {
    if (file.endsWith('.json')) {
      const filePath = path.join(directory, file);
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      try {
        const jsonData: Job = JSON.parse(fileContent);
        jobs.push(jsonData);
      } catch (error) {
        console.error(`Error parsing ${file}:`, error);
      }
    }
  }

  return jobs;
}

