{"compilerOptions": {"allowSyntheticDefaultImports": true, "declaration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "module": "commonjs", "moduleResolution": "node", "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "dist", "skipLibCheck": true, "strict": true, "target": "es2018"}, "exclude": ["dist", "node_modules"], "include": ["src", "test"]}