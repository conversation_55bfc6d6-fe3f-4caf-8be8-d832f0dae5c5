type Position = {
    x: number;
    y: number;
    z: number;
};
interface PropProps {
    model: string;
    bone?: number;
    pos: Position;
    rot: Position;
}
interface ProgressProps {
    duration: number;
    position?: 'middle' | 'bottom';
    useWhileDead?: boolean;
    allowRagdoll?: boolean;
    allowCuffed?: boolean;
    allowFalling?: boolean;
    canCancel?: boolean;
    anim?: {
        dict?: string;
        clip: string;
        flag?: number;
        blendIn?: number;
        blendOut?: number;
        duration?: number;
        playbackRate?: number;
        lockX?: boolean;
        lockY?: boolean;
        lockZ?: boolean;
        scenario?: string;
        playEnter?: boolean;
    };
    prop?: PropProps | PropProps[];
    disable?: {
        move?: boolean;
        car?: boolean;
        combat?: boolean;
        mouse?: boolean;
    };
}
interface ProgressbarProps extends Omit<ProgressProps, 'position'> {
    label: string;
}
export declare const progressBar: (data: ProgressbarProps) => Promise<boolean>;
export declare const progressCircle: (data: ProgressProps) => Promise<boolean>;
export declare const progressActive: () => boolean;
export declare const cancelProgress: () => void;
export {};
