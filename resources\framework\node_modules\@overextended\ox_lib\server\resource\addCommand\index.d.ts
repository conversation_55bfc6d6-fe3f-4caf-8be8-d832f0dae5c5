type OxCommandArguments = Record<string | number, string | number | boolean>;
interface OxCommandParams {
    name: string;
    help?: string;
    paramType?: 'number' | 'playerId' | 'string' | 'longString';
    optional?: boolean;
}
interface OxCommandProperties {
    name?: string;
    help?: string;
    params?: OxCommandParams[];
    restricted?: boolean | string | string[];
}
export declare function addCommand<T extends OxCommandArguments>(commandName: string | string[], cb: (source: number, args: T, raw: string) => Promise<any>, properties?: OxCommandProperties): void;
export {};
