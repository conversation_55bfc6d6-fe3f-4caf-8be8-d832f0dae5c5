/**
 * weapon component hash
 * refer: https://wiki.rage.mp/index.php?title=Weapons_Components
 *
 */
export var WeaponComponentHash;
(function (WeaponComponentHash) {
    // Melees
    // Knuckle Duster
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_BASE"] = 4081463091] = "COMPONENT_KNUCKLE_VARMOD_BASE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_PIMP"] = 3323197061] = "COMPONENT_KNUCKLE_VARMOD_PIMP";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_BALLAS"] = 4007263587] = "COMPONENT_KNUCKLE_VARMOD_BALLAS";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUC<PERSON>LE_VARMOD_DOLLAR"] = 1351683121] = "COMPONENT_KNUCKLE_VARMOD_DOLLAR";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_DIAMOND"] = 2539772380] = "COMPONENT_KNUCKLE_VARMOD_DIAMOND";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_HATE"] = 2112683568] = "COMPONENT_KNUCKLE_VARMOD_HATE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_LOVE"] = 1062111910] = "COMPONENT_KNUCKLE_VARMOD_LOVE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_PLAYER"] = 146278587] = "COMPONENT_KNUCKLE_VARMOD_PLAYER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_KING"] = 3800804335] = "COMPONENT_KNUCKLE_VARMOD_KING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_KNUCKLE_VARMOD_VAGOS"] = 2062808965] = "COMPONENT_KNUCKLE_VARMOD_VAGOS";
    // Switchblade
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SWITCHBLADE_VARMOD_BASE"] = 2436343040] = "COMPONENT_SWITCHBLADE_VARMOD_BASE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SWITCHBLADE_VARMOD_VAR1"] = 1530822070] = "COMPONENT_SWITCHBLADE_VARMOD_VAR1";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SWITCHBLADE_VARMOD_VAR2"] = 3885209186] = "COMPONENT_SWITCHBLADE_VARMOD_VAR2";
    // Pistols
    // Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_CLIP_01"] = 4275109233] = "COMPONENT_PISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_CLIP_02"] = 3978713628] = "COMPONENT_PISTOL_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_FLSH"] = 899381934] = "COMPONENT_AT_PI_FLSH";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_SUPP_02"] = 1709866683] = "COMPONENT_AT_PI_SUPP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_VARMOD_LUXE"] = 3610841222] = "COMPONENT_PISTOL_VARMOD_LUXE";
    // Combat Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATPISTOL_CLIP_01"] = 119648377] = "COMPONENT_COMBATPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATPISTOL_CLIP_02"] = 3598405421] = "COMPONENT_COMBATPISTOL_CLIP_02";
    // COMPONENT_AT_PI_FLSH = 0x359B7AAE, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_SUPP"] = 3271853210] = "COMPONENT_AT_PI_SUPP";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER"] = 3328527730] = "COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER";
    // AP Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_APPISTOL_CLIP_01"] = 834974250] = "COMPONENT_APPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_APPISTOL_CLIP_02"] = 614078421] = "COMPONENT_APPISTOL_CLIP_02";
    // COMPONENT_AT_PI_FLSH = 0x359B7AAE, // Flashlight
    // COMPONENT_AT_PI_SUPP = 0xC304849A, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_APPISTOL_VARMOD_LUXE"] = 2608252716] = "COMPONENT_APPISTOL_VARMOD_LUXE";
    // Pistol .50
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL50_CLIP_01"] = 580369945] = "COMPONENT_PISTOL50_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL50_CLIP_02"] = **********] = "COMPONENT_PISTOL50_CLIP_02";
    // COMPONENT_AT_PI_FLSH = 0x359B7AAE, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_SUPP_02"] = 2805810788] = "COMPONENT_AT_AR_SUPP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL50_VARMOD_LUXE"] = 2008591151] = "COMPONENT_PISTOL50_VARMOD_LUXE";
    // Heavy Revolver
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_VARMOD_BOSS"] = 384708672] = "COMPONENT_REVOLVER_VARMOD_BOSS";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_VARMOD_GOON"] = 2492708877] = "COMPONENT_REVOLVER_VARMOD_GOON";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_CLIP_01"] = 3917905123] = "COMPONENT_REVOLVER_CLIP_01";
    // SNS Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_CLIP_01"] = 4169150169] = "COMPONENT_SNSPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_CLIP_02"] = 2063610803] = "COMPONENT_SNSPISTOL_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_VARMOD_LOWRIDER"] = 2150886575] = "COMPONENT_SNSPISTOL_VARMOD_LOWRIDER";
    // Heavy Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYPISTOL_CLIP_01"] = 222992026] = "COMPONENT_HEAVYPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYPISTOL_CLIP_02"] = 1694090795] = "COMPONENT_HEAVYPISTOL_CLIP_02";
    // COMPONENT_AT_PI_FLSH = 0x359B7AAE, // Flashlight
    // COMPONENT_AT_PI_SUPP = 0xC304849A, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYPISTOL_VARMOD_LUXE"] = 2053798779] = "COMPONENT_HEAVYPISTOL_VARMOD_LUXE";
    // Heavy Revolver Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CLIP_01"] = 3122911422] = "COMPONENT_REVOLVER_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CLIP_TRACER"] = 3336103030] = "COMPONENT_REVOLVER_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CLIP_INCENDIARY"] = 15712037] = "COMPONENT_REVOLVER_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CLIP_HOLLOWPOINT"] = 284438159] = "COMPONENT_REVOLVER_MK2_CLIP_HOLLOWPOINT";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CLIP_FMJ"] = 231258687] = "COMPONENT_REVOLVER_MK2_CLIP_FMJ";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SIGHTS"] = 1108334355] = "COMPONENT_AT_SIGHTS";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MACRO_MK2"] = 77277509] = "COMPONENT_AT_SCOPE_MACRO_MK2";
    // COMPONENT_AT_PI_FLSH = 0x359B7AAE, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_COMP_03"] = 654802123] = "COMPONENT_AT_PI_COMP_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO"] = 3225415071] = "COMPONENT_REVOLVER_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_02"] = 11918884] = "COMPONENT_REVOLVER_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_03"] = 176157112] = "COMPONENT_REVOLVER_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_04"] = 4074914441] = "COMPONENT_REVOLVER_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_05"] = 288456487] = "COMPONENT_REVOLVER_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_06"] = 398658626] = "COMPONENT_REVOLVER_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_07"] = 628697006] = "COMPONENT_REVOLVER_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_08"] = 925911836] = "COMPONENT_REVOLVER_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_09"] = 1222307441] = "COMPONENT_REVOLVER_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_10"] = 552442715] = "COMPONENT_REVOLVER_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_REVOLVER_MK2_CAMO_IND_01"] = 3646023783] = "COMPONENT_REVOLVER_MK2_CAMO_IND_01";
    // SNS Pistol Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CLIP_01"] = 21392614] = "COMPONENT_SNSPISTOL_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CLIP_02"] = 3465283442] = "COMPONENT_SNSPISTOL_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CLIP_TRACER"] = 2418909806] = "COMPONENT_SNSPISTOL_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CLIP_INCENDIARY"] = 3870121849] = "COMPONENT_SNSPISTOL_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CLIP_HOLLOWPOINT"] = 2366665730] = "COMPONENT_SNSPISTOL_MK2_CLIP_HOLLOWPOINT";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CLIP_FMJ"] = 3239176998] = "COMPONENT_SNSPISTOL_MK2_CLIP_FMJ";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_FLSH_03"] = 1246324211] = "COMPONENT_AT_PI_FLSH_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_RAIL_02"] = 1205768792] = "COMPONENT_AT_PI_RAIL_02";
    // COMPONENT_AT_PI_SUPP_02 = 0x65EA7EBB, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_COMP_02"] = 2860680127] = "COMPONENT_AT_PI_COMP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO"] = 259780317] = "COMPONENT_SNSPISTOL_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_02"] = 2321624822] = "COMPONENT_SNSPISTOL_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_03"] = 1996130345] = "COMPONENT_SNSPISTOL_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_04"] = 2839309484] = "COMPONENT_SNSPISTOL_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_05"] = 2626704212] = "COMPONENT_SNSPISTOL_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_06"] = 1308243489] = "COMPONENT_SNSPISTOL_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_07"] = 1122574335] = "COMPONENT_SNSPISTOL_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_08"] = 1420313469] = "COMPONENT_SNSPISTOL_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_09"] = 109848390] = "COMPONENT_SNSPISTOL_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_10"] = 593945703] = "COMPONENT_SNSPISTOL_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_IND_01"] = 1142457062] = "COMPONENT_SNSPISTOL_MK2_CAMO_IND_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_SLIDE"] = 3891161322] = "COMPONENT_SNSPISTOL_MK2_CAMO_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_02_SLIDE"] = 691432737] = "COMPONENT_SNSPISTOL_MK2_CAMO_02_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_03_SLIDE"] = 987648331] = "COMPONENT_SNSPISTOL_MK2_CAMO_03_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_04_SLIDE"] = 3863286761] = "COMPONENT_SNSPISTOL_MK2_CAMO_04_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_05_SLIDE"] = 3447384986] = "COMPONENT_SNSPISTOL_MK2_CAMO_05_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_06_SLIDE"] = 4202375078] = "COMPONENT_SNSPISTOL_MK2_CAMO_06_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_07_SLIDE"] = 3800418970] = "COMPONENT_SNSPISTOL_MK2_CAMO_07_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_08_SLIDE"] = 730876697] = "COMPONENT_SNSPISTOL_MK2_CAMO_08_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_09_SLIDE"] = 583159708] = "COMPONENT_SNSPISTOL_MK2_CAMO_09_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_10_SLIDE"] = 2366463693] = "COMPONENT_SNSPISTOL_MK2_CAMO_10_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNSPISTOL_MK2_CAMO_IND_01_SLIDE"] = 520557834] = "COMPONENT_SNSPISTOL_MK2_CAMO_IND_01_SLIDE";
    // Pistol Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CLIP_01"] = 2499030370] = "COMPONENT_PISTOL_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CLIP_02"] = 1591132456] = "COMPONENT_PISTOL_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CLIP_TRACER"] = 634039983] = "COMPONENT_PISTOL_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CLIP_INCENDIARY"] = 733837882] = "COMPONENT_PISTOL_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CLIP_HOLLOWPOINT"] = 2248057097] = "COMPONENT_PISTOL_MK2_CLIP_HOLLOWPOINT";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CLIP_FMJ"] = 1329061674] = "COMPONENT_PISTOL_MK2_CLIP_FMJ";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_RAIL"] = 2396306288] = "COMPONENT_AT_PI_RAIL";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_FLSH_02"] = 1140676955] = "COMPONENT_AT_PI_FLSH_02";
    // COMPONENT_AT_PI_SUPP_02 = 0x65EA7EBB, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_PI_COMP"] = 568543123] = "COMPONENT_AT_PI_COMP";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO"] = 1550611612] = "COMPONENT_PISTOL_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_02"] = 368550800] = "COMPONENT_PISTOL_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_03"] = 2525897947] = "COMPONENT_PISTOL_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_04"] = 24902297] = "COMPONENT_PISTOL_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_05"] = 4066925682] = "COMPONENT_PISTOL_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_06"] = 3710005734] = "COMPONENT_PISTOL_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_07"] = 3141791350] = "COMPONENT_PISTOL_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_08"] = 1301287696] = "COMPONENT_PISTOL_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_09"] = 1597093459] = "COMPONENT_PISTOL_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_10"] = 1769871776] = "COMPONENT_PISTOL_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_IND_01"] = 2467084625] = "COMPONENT_PISTOL_MK2_CAMO_IND_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_SLIDE"] = 3036451504] = "COMPONENT_PISTOL_MK2_CAMO_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_02_SLIDE"] = 438243936] = "COMPONENT_PISTOL_MK2_CAMO_02_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_03_SLIDE"] = 3839888240] = "COMPONENT_PISTOL_MK2_CAMO_03_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_04_SLIDE"] = 740920107] = "COMPONENT_PISTOL_MK2_CAMO_04_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_05_SLIDE"] = 3753350949] = "COMPONENT_PISTOL_MK2_CAMO_05_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_06_SLIDE"] = 1809261196] = "COMPONENT_PISTOL_MK2_CAMO_06_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_07_SLIDE"] = 2648428428] = "COMPONENT_PISTOL_MK2_CAMO_07_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_08_SLIDE"] = 3004802348] = "COMPONENT_PISTOL_MK2_CAMO_08_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_09_SLIDE"] = 3330502162] = "COMPONENT_PISTOL_MK2_CAMO_09_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_10_SLIDE"] = 1135718771] = "COMPONENT_PISTOL_MK2_CAMO_10_SLIDE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PISTOL_MK2_CAMO_IND_01_SLIDE"] = 1253942266] = "COMPONENT_PISTOL_MK2_CAMO_IND_01_SLIDE";
    // Vintage Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_VINTAGEPISTOL_CLIP_01"] = 1168357051] = "COMPONENT_VINTAGEPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_VINTAGEPISTOL_CLIP_02"] = 867832552] = "COMPONENT_VINTAGEPISTOL_CLIP_02";
    // COMPONENT_AT_PI_SUPP = 0xC304849A, // Suppressor
    // Up-n-Atomizer
    WeaponComponentHash[WeaponComponentHash["COMPONENT_RAYPISTOL_VARMOD_XMAS18"] = 3621517063] = "COMPONENT_RAYPISTOL_VARMOD_XMAS18";
    // Ceramic Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CERAMICPISTOL_CLIP_01"] = 1423184737] = "COMPONENT_CERAMICPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CERAMICPISTOL_CLIP_02"] = 2172153001] = "COMPONENT_CERAMICPISTOL_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CERAMICPISTOL_SUPP"] = 2466764538] = "COMPONENT_CERAMICPISTOL_SUPP";
    // Submachine Guns
    // Micro SMG
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MICROSMG_CLIP_01"] = **********] = "COMPONENT_MICROSMG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MICROSMG_CLIP_02"] = 283556395] = "COMPONENT_MICROSMG_CLIP_02";
    // COMPONENT_AT_PI_FLSH = 0x359B7AAE, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MACRO"] = 2637152041] = "COMPONENT_AT_SCOPE_MACRO";
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MICROSMG_VARMOD_LUXE"] = **********] = "COMPONENT_MICROSMG_VARMOD_LUXE";
    // SMG
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_CLIP_01"] = 643254679] = "COMPONENT_SMG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_CLIP_02"] = 889808635] = "COMPONENT_SMG_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_CLIP_03"] = 2043113590] = "COMPONENT_SMG_CLIP_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_FLSH"] = 2076495324] = "COMPONENT_AT_AR_FLSH";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MACRO_02"] = 1019656791] = "COMPONENT_AT_SCOPE_MACRO_02";
    // COMPONENT_AT_PI_SUPP = 0xC304849A, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_VARMOD_LUXE"] = 663170192] = "COMPONENT_SMG_VARMOD_LUXE";
    // Assault SMG
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTSMG_CLIP_01"] = 2366834608] = "COMPONENT_ASSAULTSMG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTSMG_CLIP_02"] = 3141985303] = "COMPONENT_ASSAULTSMG_CLIP_02";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SCOPE_MACRO = 0x9D2FBF29, // Scope
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTSMG_VARMOD_LOWRIDER"] = 663517359] = "COMPONENT_ASSAULTSMG_VARMOD_LOWRIDER";
    // Mini SMG
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MINISMG_CLIP_01"] = 2227745491] = "COMPONENT_MINISMG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MINISMG_CLIP_02"] = 2474561719] = "COMPONENT_MINISMG_CLIP_02";
    // SMG Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CLIP_01"] = 1277460590] = "COMPONENT_SMG_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CLIP_02"] = 3112393518] = "COMPONENT_SMG_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CLIP_TRACER"] = 2146055916] = "COMPONENT_SMG_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CLIP_INCENDIARY"] = 3650233061] = "COMPONENT_SMG_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CLIP_HOLLOWPOINT"] = 974903034] = "COMPONENT_SMG_MK2_CLIP_HOLLOWPOINT";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CLIP_FMJ"] = 190476639] = "COMPONENT_SMG_MK2_CLIP_FMJ";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SIGHTS_SMG"] = 2681951826] = "COMPONENT_AT_SIGHTS_SMG";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MACRO_02_SMG_MK2"] = 3842157419] = "COMPONENT_AT_SCOPE_MACRO_02_SMG_MK2";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_SMALL_SMG_MK2"] = 1038927834] = "COMPONENT_AT_SCOPE_SMALL_SMG_MK2";
    // COMPONENT_AT_PI_SUPP = 0xC304849A, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_01"] = 3113485012] = "COMPONENT_AT_MUZZLE_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_02"] = 3362234491] = "COMPONENT_AT_MUZZLE_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_03"] = 3725708239] = "COMPONENT_AT_MUZZLE_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_04"] = 3968886988] = "COMPONENT_AT_MUZZLE_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_05"] = 48731514] = "COMPONENT_AT_MUZZLE_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_06"] = 880736428] = "COMPONENT_AT_MUZZLE_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_07"] = 1303784126] = "COMPONENT_AT_MUZZLE_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SB_BARREL_01"] = 3641720545] = "COMPONENT_AT_SB_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SB_BARREL_02"] = 2774849419] = "COMPONENT_AT_SB_BARREL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO"] = 3298267239] = "COMPONENT_SMG_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_02"] = 940943685] = "COMPONENT_SMG_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_03"] = 1263226800] = "COMPONENT_SMG_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_04"] = 3966931456] = "COMPONENT_SMG_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_05"] = 1224100642] = "COMPONENT_SMG_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_06"] = 899228776] = "COMPONENT_SMG_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_07"] = 616006309] = "COMPONENT_SMG_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_08"] = 2733014785] = "COMPONENT_SMG_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_09"] = 572063080] = "COMPONENT_SMG_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_10"] = 1170588613] = "COMPONENT_SMG_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SMG_MK2_CAMO_IND_01"] = 966612367] = "COMPONENT_SMG_MK2_CAMO_IND_01";
    // Machine Pistol
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MACHINEPISTOL_CLIP_01"] = **********] = "COMPONENT_MACHINEPISTOL_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MACHINEPISTOL_CLIP_02"] = **********] = "COMPONENT_MACHINEPISTOL_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MACHINEPISTOL_CLIP_03"] = **********] = "COMPONENT_MACHINEPISTOL_CLIP_03";
    // COMPONENT_AT_PI_SUPP = 0xC304849A, // Suppressor
    // Combat PDW
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATPDW_CLIP_01"] = 1125642654] = "COMPONENT_COMBATPDW_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATPDW_CLIP_02"] = 860508675] = "COMPONENT_COMBATPDW_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATPDW_CLIP_03"] = 1857603803] = "COMPONENT_COMBATPDW_CLIP_03";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_AFGRIP"] = 202788691] = "COMPONENT_AT_AR_AFGRIP";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_SMALL"] = 2855028148] = "COMPONENT_AT_SCOPE_SMALL";
    // Shotguns
    // Pump Shotgun
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SR_SUPP"] = 3859329886] = "COMPONENT_AT_SR_SUPP";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER"] = 2732039643] = "COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER";
    // Sawed-Off Shotgun
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE"] = 2242268665] = "COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE";
    // Assault Shotgun
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTSHOTGUN_CLIP_01"] = 2498239431] = "COMPONENT_ASSAULTSHOTGUN_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTSHOTGUN_CLIP_02"] = 2260565874] = "COMPONENT_ASSAULTSHOTGUN_CLIP_02";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_SUPP"] = 2205435306] = "COMPONENT_AT_AR_SUPP";
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    // Bullpup Shotgun
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    // Pump Shotgun Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CLIP_01"] = 3449028929] = "COMPONENT_PUMPSHOTGUN_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CLIP_INCENDIARY"] = 2676628469] = "COMPONENT_PUMPSHOTGUN_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CLIP_ARMORPIERCING"] = 1315288101] = "COMPONENT_PUMPSHOTGUN_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CLIP_HOLLOWPOINT"] = 3914869031] = "COMPONENT_PUMPSHOTGUN_MK2_CLIP_HOLLOWPOINT";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CLIP_EXPLOSIVE"] = 1004815965] = "COMPONENT_PUMPSHOTGUN_MK2_CLIP_EXPLOSIVE";
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    // COMPONENT_AT_SCOPE_MACRO_MK2 = 0x49B2945, // Small Scope
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_SMALL_MK2"] = 1060929921] = "COMPONENT_AT_SCOPE_SMALL_MK2";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SR_SUPP_03"] = 2890063729] = "COMPONENT_AT_SR_SUPP_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_08"] = 1602080333] = "COMPONENT_AT_MUZZLE_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO"] = 3820854852] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_02"] = 387223451] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_03"] = 617753366] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_04"] = 4072589040] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_05"] = 8741501] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_06"] = 3693681093] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_07"] = 3783533691] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_08"] = 3639579478] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_09"] = 4012490698] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_10"] = 1739501925] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_PUMPSHOTGUN_MK2_CAMO_IND_01"] = 1178671645] = "COMPONENT_PUMPSHOTGUN_MK2_CAMO_IND_01";
    // Heavy Shotgun
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSHOTGUN_CLIP_01"] = 844049759] = "COMPONENT_HEAVYSHOTGUN_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSHOTGUN_CLIP_02"] = 2535257853] = "COMPONENT_HEAVYSHOTGUN_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSHOTGUN_CLIP_03"] = 2294798931] = "COMPONENT_HEAVYSHOTGUN_CLIP_03";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    // Combat Shotgun
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // Rifles
    // Assault Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_CLIP_01"] = 3193891350] = "COMPONENT_ASSAULTRIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_CLIP_02"] = 2971750299] = "COMPONENT_ASSAULTRIFLE_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_CLIP_03"] = 3689981245] = "COMPONENT_ASSAULTRIFLE_CLIP_03";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SCOPE_MACRO = 0x9D2FBF29, // Scope
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_VARMOD_LUXE"] = 1319990579] = "COMPONENT_ASSAULTRIFLE_VARMOD_LUXE";
    // Carbine Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_CLIP_01"] = 2680042476] = "COMPONENT_CARBINERIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_CLIP_02"] = 2433783441] = "COMPONENT_CARBINERIFLE_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_CLIP_03"] = 3127044405] = "COMPONENT_CARBINERIFLE_CLIP_03";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MEDIUM"] = 2698550338] = "COMPONENT_AT_SCOPE_MEDIUM";
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_VARMOD_LUXE"] = 3634075224] = "COMPONENT_CARBINERIFLE_VARMOD_LUXE";
    // Advanced Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ADVANCEDRIFLE_CLIP_01"] = 4203716879] = "COMPONENT_ADVANCEDRIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ADVANCEDRIFLE_CLIP_02"] = 2395064697] = "COMPONENT_ADVANCEDRIFLE_CLIP_02";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SCOPE_SMALL = 0xAA2C45B4, // Scope
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE"] = 930927479] = "COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE";
    // Special Carbine
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_CLIP_01"] = 3334989185] = "COMPONENT_SPECIALCARBINE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_CLIP_02"] = 2089537806] = "COMPONENT_SPECIALCARBINE_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_CLIP_03"] = 1801039530] = "COMPONENT_SPECIALCARBINE_CLIP_03";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SCOPE_MEDIUM = 0xA0D89C42, // Scope
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_VARMOD_LOWRIDER"] = 1929467122] = "COMPONENT_SPECIALCARBINE_VARMOD_LOWRIDER";
    // Bullpup Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_CLIP_01"] = 3315675008] = "COMPONENT_BULLPUPRIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_CLIP_02"] = 3009973007] = "COMPONENT_BULLPUPRIFLE_CLIP_02";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SCOPE_SMALL = 0xAA2C45B4, // Scope
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_VARMOD_LOW"] = 2824322168] = "COMPONENT_BULLPUPRIFLE_VARMOD_LOW";
    // Bullpup Rifle Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CLIP_01"] = 25766362] = "COMPONENT_BULLPUPRIFLE_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CLIP_02"] = 4021290536] = "COMPONENT_BULLPUPRIFLE_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CLIP_TRACER"] = 2183159977] = "COMPONENT_BULLPUPRIFLE_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CLIP_INCENDIARY"] = 2845636954] = "COMPONENT_BULLPUPRIFLE_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CLIP_ARMORPIERCING"] = 4205311469] = "COMPONENT_BULLPUPRIFLE_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CLIP_FMJ"] = 1130501904] = "COMPONENT_BULLPUPRIFLE_MK2_CLIP_FMJ";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MACRO_02_MK2"] = 3350057221] = "COMPONENT_AT_SCOPE_MACRO_02_MK2";
    // COMPONENT_AT_SCOPE_SMALL_MK2 = 0x3F3C8181, // Medium Scope
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_BP_BARREL_01"] = 1704640795] = "COMPONENT_AT_BP_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_BP_BARREL_02"] = 1005743559] = "COMPONENT_AT_BP_BARREL_02";
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // COMPONENT_AT_MUZZLE_01 = 0xB99402D4, // Flat Muzzle Brake
    // COMPONENT_AT_MUZZLE_02 = 0xC867A07B, // Tactical Muzzle Brake
    // COMPONENT_AT_MUZZLE_03 = 0xDE11CBCF, // Fat-End Muzzle Brake
    // COMPONENT_AT_MUZZLE_04 = 0xEC9068CC, // Precision Muzzle Brake
    // COMPONENT_AT_MUZZLE_05 = 0x2E7957A, // Heavy Duty Muzzle Brake
    // COMPONENT_AT_MUZZLE_06 = 0x347EF8AC, // Slanted Muzzle Brake
    // COMPONENT_AT_MUZZLE_07 = 0x4DB62ABE, // Split-End Muzzle Brake
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_AFGRIP_02"] = 2640679034] = "COMPONENT_AT_AR_AFGRIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO"] = 2923451831] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_02"] = 3104173419] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_03"] = 2797881576] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_04"] = 2491819116] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_05"] = 2318995410] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_06"] = 36929477] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_07"] = 4026522462] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_08"] = 3720197850] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_09"] = 3412267557] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_10"] = 2826785822] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_BULLPUPRIFLE_MK2_CAMO_IND_01"] = 3320426066] = "COMPONENT_BULLPUPRIFLE_MK2_CAMO_IND_01";
    // Special Carbine Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CLIP_01"] = 382112385] = "COMPONENT_SPECIALCARBINE_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CLIP_02"] = 3726614828] = "COMPONENT_SPECIALCARBINE_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CLIP_TRACER"] = 2271594122] = "COMPONENT_SPECIALCARBINE_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CLIP_INCENDIARY"] = 3724612230] = "COMPONENT_SPECIALCARBINE_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CLIP_ARMORPIERCING"] = 1362433589] = "COMPONENT_SPECIALCARBINE_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CLIP_FMJ"] = 1346235024] = "COMPONENT_SPECIALCARBINE_MK2_CLIP_FMJ";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    // COMPONENT_AT_SCOPE_MACRO_MK2 = 0x49B2945, // Small Scope
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MEDIUM_MK2"] = 3328927042] = "COMPONENT_AT_SCOPE_MEDIUM_MK2";
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    // COMPONENT_AT_MUZZLE_01 = 0xB99402D4, // Flat Muzzle Brake
    // COMPONENT_AT_MUZZLE_02 = 0xC867A07B, // Tactical Muzzle Brake
    // COMPONENT_AT_MUZZLE_03 = 0xDE11CBCF, // Fat-End Muzzle Brake
    // COMPONENT_AT_MUZZLE_04 = 0xEC9068CC, // Precision Muzzle Brake
    // COMPONENT_AT_MUZZLE_05 = 0x2E7957A, // Heavy Duty Muzzle Brake
    // COMPONENT_AT_MUZZLE_06 = 0x347EF8AC, // Slanted Muzzle Brake
    // COMPONENT_AT_MUZZLE_07 = 0x4DB62ABE, // Split-End Muzzle Brake
    // COMPONENT_AT_AR_AFGRIP_02 = 0x9D65907A, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SC_BARREL_01"] = 3879097257] = "COMPONENT_AT_SC_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SC_BARREL_02"] = 4185880635] = "COMPONENT_AT_SC_BARREL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO"] = 3557537083] = "COMPONENT_SPECIALCARBINE_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_02"] = 1125852043] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_03"] = 886015732] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_04"] = 3032680157] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_05"] = 3999758885] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_06"] = 3750812792] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_07"] = 172765678] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_08"] = 2312089847] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_09"] = 2072122460] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_10"] = 2308747125] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SPECIALCARBINE_MK2_CAMO_IND_01"] = 1377355801] = "COMPONENT_SPECIALCARBINE_MK2_CAMO_IND_01";
    // Assault Rifle Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CLIP_01"] = 2249208895] = "COMPONENT_ASSAULTRIFLE_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CLIP_02"] = 3509242479] = "COMPONENT_ASSAULTRIFLE_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CLIP_TRACER"] = 4012669121] = "COMPONENT_ASSAULTRIFLE_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CLIP_INCENDIARY"] = 4218476627] = "COMPONENT_ASSAULTRIFLE_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CLIP_ARMORPIERCING"] = 2816286296] = "COMPONENT_ASSAULTRIFLE_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CLIP_FMJ"] = 1675665560] = "COMPONENT_ASSAULTRIFLE_MK2_CLIP_FMJ";
    // COMPONENT_AT_AR_AFGRIP_02 = 0x9D65907A, // Grip
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    // COMPONENT_AT_SCOPE_MACRO_MK2 = 0x49B2945, // Small Scope
    // COMPONENT_AT_SCOPE_MEDIUM_MK2 = 0xC66B6542, // Large Scope
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    // COMPONENT_AT_MUZZLE_01 = 0xB99402D4, // Flat Muzzle Brake
    // COMPONENT_AT_MUZZLE_02 = 0xC867A07B, // Tactical Muzzle Brake
    // COMPONENT_AT_MUZZLE_03 = 0xDE11CBCF, // Fat-End Muzzle Brake
    //  COMPONENT_AT_MUZZLE_04 = 0xEC9068CC, // Precision Muzzle Brake
    // COMPONENT_AT_MUZZLE_05 = 0x2E7957A, // Heavy Duty Muzzle Brake
    // COMPONENT_AT_MUZZLE_06 = 0x347EF8AC, // Slanted Muzzle Brake
    // COMPONENT_AT_MUZZLE_07 = 0x4DB62ABE, // Split-End Muzzle Brake
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_BARREL_01"] = 1134861606] = "COMPONENT_AT_AR_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_AR_BARREL_02"] = 1447477866] = "COMPONENT_AT_AR_BARREL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO"] = 2434475183] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_02"] = 937772107] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_03"] = 1401650071] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_04"] = 628662130] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_05"] = 3309920045] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_06"] = 3482022833] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_07"] = 2847614993] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_08"] = 4234628436] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_09"] = 2088750491] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_10"] = 2781053842] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_ASSAULTRIFLE_MK2_CAMO_IND_01"] = 3115408816] = "COMPONENT_ASSAULTRIFLE_MK2_CAMO_IND_01";
    // Carbine Rifle Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CLIP_01"] = 1283078430] = "COMPONENT_CARBINERIFLE_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CLIP_02"] = 1574296533] = "COMPONENT_CARBINERIFLE_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CLIP_TRACER"] = 391640422] = "COMPONENT_CARBINERIFLE_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CLIP_INCENDIARY"] = 1025884839] = "COMPONENT_CARBINERIFLE_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CLIP_ARMORPIERCING"] = 626875735] = "COMPONENT_CARBINERIFLE_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CLIP_FMJ"] = 1141059345] = "COMPONENT_CARBINERIFLE_MK2_CLIP_FMJ";
    // COMPONENT_AT_AR_AFGRIP_02 = 0x9D65907A, // Grip
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    // COMPONENT_AT_SCOPE_MACRO_MK2 = 0x49B2945, // Small Scope
    // COMPONENT_AT_SCOPE_MEDIUM_MK2 = 0xC66B6542, // Large Scope
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // COMPONENT_AT_MUZZLE_01 = 0xB99402D4, // Flat Muzzle Brake
    // COMPONENT_AT_MUZZLE_02 = 0xC867A07B, // Tactical Muzzle Brake
    // COMPONENT_AT_MUZZLE_03 = 0xDE11CBCF, // Fat-End Muzzle Brake
    // COMPONENT_AT_MUZZLE_04 = 0xEC9068CC, // Precision Muzzle Brake
    // COMPONENT_AT_MUZZLE_05 = 0x2E7957A, // Heavy Duty Muzzle Brake
    // COMPONENT_AT_MUZZLE_06 = 0x347EF8AC, // Slanted Muzzle Brake
    // COMPONENT_AT_MUZZLE_07 = 0x4DB62ABE, // Split-End Muzzle Brake
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_CR_BARREL_01"] = 2201368575] = "COMPONENT_AT_CR_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_CR_BARREL_02"] = 2335983627] = "COMPONENT_AT_CR_BARREL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO"] = 1272803094] = "COMPONENT_CARBINERIFLE_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_02"] = 1080719624] = "COMPONENT_CARBINERIFLE_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_03"] = 792221348] = "COMPONENT_CARBINERIFLE_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_04"] = 3842785869] = "COMPONENT_CARBINERIFLE_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_05"] = 3548192559] = "COMPONENT_CARBINERIFLE_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_06"] = 2250671235] = "COMPONENT_CARBINERIFLE_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_07"] = 4095795318] = "COMPONENT_CARBINERIFLE_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_08"] = 2866892280] = "COMPONENT_CARBINERIFLE_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_09"] = 2559813981] = "COMPONENT_CARBINERIFLE_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_10"] = 1796459838] = "COMPONENT_CARBINERIFLE_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_CARBINERIFLE_MK2_CAMO_IND_01"] = 3663056191] = "COMPONENT_CARBINERIFLE_MK2_CAMO_IND_01";
    // Compact Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMPACTRIFLE_CLIP_01"] = 1363085923] = "COMPONENT_COMPACTRIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMPACTRIFLE_CLIP_02"] = 1509923832] = "COMPONENT_COMPACTRIFLE_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMPACTRIFLE_CLIP_03"] = 3322377230] = "COMPONENT_COMPACTRIFLE_CLIP_03";
    // Military Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MILITARYRIFLE_CLIP_01"] = 759617595] = "COMPONENT_MILITARYRIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MILITARYRIFLE_CLIP_02"] = 1749732930] = "COMPONENT_MILITARYRIFLE_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MILITARYRIFLE_SIGHT_01"] = 1803744149] = "COMPONENT_MILITARYRIFLE_SIGHT_01";
    // COMPONENT_AT_SCOPE_SMALL = 0xAA2C45B4, // Scope
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // Machine Guns
    // MG
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MG_CLIP_01"] = 4097109892] = "COMPONENT_MG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MG_CLIP_02"] = **********] = "COMPONENT_MG_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_SMALL_02"] = 1006677997] = "COMPONENT_AT_SCOPE_SMALL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MG_VARMOD_LOWRIDER"] = **********] = "COMPONENT_MG_VARMOD_LOWRIDER";
    // Combat MG
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_CLIP_01"] = 3791631178] = "COMPONENT_COMBATMG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_CLIP_02"] = 3603274966] = "COMPONENT_COMBATMG_CLIP_02";
    // COMPONENT_AT_SCOPE_MEDIUM = 0xA0D89C42, // Scope
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_VARMOD_LOWRIDER"] = 2466172125] = "COMPONENT_COMBATMG_VARMOD_LOWRIDER";
    // Combat MG Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CLIP_01"] = 1227564412] = "COMPONENT_COMBATMG_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CLIP_02"] = 400507625] = "COMPONENT_COMBATMG_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CLIP_TRACER"] = 4133787461] = "COMPONENT_COMBATMG_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CLIP_INCENDIARY"] = 3274096058] = "COMPONENT_COMBATMG_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CLIP_ARMORPIERCING"] = 696788003] = "COMPONENT_COMBATMG_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CLIP_FMJ"] = 1475288264] = "COMPONENT_COMBATMG_MK2_CLIP_FMJ";
    // COMPONENT_AT_AR_AFGRIP_02 = 0x9D65907A, // Grip
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    // COMPONENT_AT_SCOPE_SMALL_MK2 = 0x3F3C8181, // Medium Scope
    // COMPONENT_AT_SCOPE_MEDIUM_MK2 = 0xC66B6542, // Large Scope
    // COMPONENT_AT_MUZZLE_01 = 0xB99402D4, // Flat Muzzle Brake
    // COMPONENT_AT_MUZZLE_02 = 0xC867A07B, // Tactical Muzzle Brake
    // COMPONENT_AT_MUZZLE_03 = 0xDE11CBCF, // Fat-End Muzzle Brake
    // COMPONENT_AT_MUZZLE_04 = 0xEC9068CC, // Precision Muzzle Brake
    // COMPONENT_AT_MUZZLE_05 = 0x2E7957A, // Heavy Duty Muzzle Brake
    // COMPONENT_AT_MUZZLE_06 = 0x347EF8AC, // Slanted Muzzle Brake
    // COMPONENT_AT_MUZZLE_07 = 0x4DB62ABE, // Split-End Muzzle Brake
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MG_BARREL_01"] = 3276730932] = "COMPONENT_AT_MG_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MG_BARREL_02"] = 3051509595] = "COMPONENT_AT_MG_BARREL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO"] = 1249283253] = "COMPONENT_COMBATMG_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_02"] = 3437259709] = "COMPONENT_COMBATMG_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_03"] = 3197423398] = "COMPONENT_COMBATMG_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_04"] = 1980349969] = "COMPONENT_COMBATMG_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_05"] = 1219453777] = "COMPONENT_COMBATMG_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_06"] = 2441508106] = "COMPONENT_COMBATMG_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_07"] = 2220186280] = "COMPONENT_COMBATMG_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_08"] = 457967755] = "COMPONENT_COMBATMG_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_09"] = 235171324] = "COMPONENT_COMBATMG_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_10"] = 42685294] = "COMPONENT_COMBATMG_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_COMBATMG_MK2_CAMO_IND_01"] = 3607349581] = "COMPONENT_COMBATMG_MK2_CAMO_IND_01";
    // Gusenberg Sweeper
    WeaponComponentHash[WeaponComponentHash["COMPONENT_GUSENBERG_CLIP_01"] = 484812453] = "COMPONENT_GUSENBERG_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_GUSENBERG_CLIP_02"] = 3939025520] = "COMPONENT_GUSENBERG_CLIP_02";
    // Sniper Rifles
    // Sniper Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNIPERRIFLE_CLIP_01"] = 2613461129] = "COMPONENT_SNIPERRIFLE_CLIP_01";
    // COMPONENT_AT_AR_SUPP_02 = 0xA73D4664, // Suppressor
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_LARGE"] = 3527687644] = "COMPONENT_AT_SCOPE_LARGE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_MAX"] = 3159677559] = "COMPONENT_AT_SCOPE_MAX";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_SNIPERRIFLE_VARMOD_LUXE"] = 1077065191] = "COMPONENT_SNIPERRIFLE_VARMOD_LUXE";
    // Heavy Sniper
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_CLIP_01"] = 1198478068] = "COMPONENT_HEAVYSNIPER_CLIP_01";
    // COMPONENT_AT_SCOPE_LARGE = 0xD2443DDC, // Scope
    // COMPONENT_AT_SCOPE_MAX = 0xBC54DA77, // Advanced Scope
    // Marksman Rifle Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CLIP_01"] = 2497785294] = "COMPONENT_MARKSMANRIFLE_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CLIP_02"] = 3872379306] = "COMPONENT_MARKSMANRIFLE_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CLIP_TRACER"] = 3615105746] = "COMPONENT_MARKSMANRIFLE_MK2_CLIP_TRACER";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CLIP_INCENDIARY"] = 1842849902] = "COMPONENT_MARKSMANRIFLE_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CLIP_ARMORPIERCING"] = 4100968569] = "COMPONENT_MARKSMANRIFLE_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CLIP_FMJ"] = 3779763923] = "COMPONENT_MARKSMANRIFLE_MK2_CLIP_FMJ";
    // COMPONENT_AT_SIGHTS = 0x420FD713, // Holographic Sight
    // COMPONENT_AT_SCOPE_MEDIUM_MK2 = 0xC66B6542, // Large Scope
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM_MK2"] = 1528590652] = "COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM_MK2";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // COMPONENT_AT_MUZZLE_01 = 0xB99402D4, // Flat Muzzle Brake
    // COMPONENT_AT_MUZZLE_02 = 0xC867A07B, // Tactical Muzzle Brake
    // COMPONENT_AT_MUZZLE_03 = 0xDE11CBCF, // Fat-End Muzzle Brake
    // COMPONENT_AT_MUZZLE_04 = 0xEC9068CC, // Precision Muzzle Brake
    // COMPONENT_AT_MUZZLE_05 = 0x2E7957A, // Heavy Duty Muzzle Brake
    // COMPONENT_AT_MUZZLE_06 = 0x347EF8AC, // Slanted Muzzle Brake
    // COMPONENT_AT_MUZZLE_07 = 0x4DB62ABE, // Split-End Muzzle Brake
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MRFL_BARREL_01"] = 941317513] = "COMPONENT_AT_MRFL_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MRFL_BARREL_02"] = 1748450780] = "COMPONENT_AT_MRFL_BARREL_02";
    // COMPONENT_AT_AR_AFGRIP_02 = 0x9D65907A, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO"] = 2425682848] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_02"] = 1931539634] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_03"] = 1624199183] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_04"] = 4268133183] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_05"] = 4084561241] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_06"] = 423313640] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_07"] = 276639596] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_08"] = 3303610433] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_09"] = 2612118995] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_10"] = 996213771] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_MK2_CAMO_IND_01"] = 3080918746] = "COMPONENT_MARKSMANRIFLE_MK2_CAMO_IND_01";
    // Heavy Sniper Mk II
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CLIP_01"] = 4196276776] = "COMPONENT_HEAVYSNIPER_MK2_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CLIP_02"] = 752418717] = "COMPONENT_HEAVYSNIPER_MK2_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CLIP_INCENDIARY"] = 247526935] = "COMPONENT_HEAVYSNIPER_MK2_CLIP_INCENDIARY";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CLIP_ARMORPIERCING"] = 4164277972] = "COMPONENT_HEAVYSNIPER_MK2_CLIP_ARMORPIERCING";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CLIP_FMJ"] = 1005144310] = "COMPONENT_HEAVYSNIPER_MK2_CLIP_FMJ";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CLIP_EXPLOSIVE"] = 2313935527] = "COMPONENT_HEAVYSNIPER_MK2_CLIP_EXPLOSIVE";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_LARGE_MK2"] = 2193687427] = "COMPONENT_AT_SCOPE_LARGE_MK2";
    // COMPONENT_AT_SCOPE_MAX = 0xBC54DA77, // Advanced Scope
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_NV"] = 3061846192] = "COMPONENT_AT_SCOPE_NV";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_THERMAL"] = 776198721] = "COMPONENT_AT_SCOPE_THERMAL";
    // COMPONENT_AT_SR_SUPP_03 = 0xAC42DF71, // Suppressor
    // COMPONENT_AT_MUZZLE_08 = 0x5F7DCE4D, // Squared Muzzle Brake
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_MUZZLE_09"] = 1764221345] = "COMPONENT_AT_MUZZLE_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SR_BARREL_01"] = 2425761975] = "COMPONENT_AT_SR_BARREL_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SR_BARREL_02"] = 277524638] = "COMPONENT_AT_SR_BARREL_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO"] = 4164123906] = "COMPONENT_HEAVYSNIPER_MK2_CAMO";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_02"] = 3317620069] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_03"] = 3916506229] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_03";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_04"] = 329939175] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_04";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_05"] = 643374672] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_05";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_06"] = 807875052] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_06";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_07"] = 2893163128] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_07";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_08"] = 3198471901] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_08";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_09"] = 3447155842] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_09";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_10"] = 2881858759] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_10";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_HEAVYSNIPER_MK2_CAMO_IND_01"] = 1815270123] = "COMPONENT_HEAVYSNIPER_MK2_CAMO_IND_01";
    // Marksman Rifle
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_CLIP_01"] = **********] = "COMPONENT_MARKSMANRIFLE_CLIP_01";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_CLIP_02"] = **********] = "COMPONENT_MARKSMANRIFLE_CLIP_02";
    WeaponComponentHash[WeaponComponentHash["COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM"] = 471997210] = "COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_SUPP = 0x837445AA, // Suppressor
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    WeaponComponentHash[WeaponComponentHash["COMPONENT_MARKSMANRIFLE_VARMOD_LUXE"] = 371102273] = "COMPONENT_MARKSMANRIFLE_VARMOD_LUXE";
    // Heavy Weapons
    // Grenade Launcher
    WeaponComponentHash[WeaponComponentHash["COMPONENT_GRENADELAUNCHER_CLIP_01"] = 296639639] = "COMPONENT_GRENADELAUNCHER_CLIP_01";
    // COMPONENT_AT_AR_FLSH = 0x7BC4CDDC, // Flashlight
    // COMPONENT_AT_AR_AFGRIP = 0xC164F53, // Grip
    // COMPONENT_AT_SCOPE_SMALL = 0xAA2C45B4, // Scope
    // Invalid
    WeaponComponentHash[WeaponComponentHash["Invalid"] = 4294967295] = "Invalid";
    WeaponComponentHash[WeaponComponentHash["AdvancedRifleClip01"] = 4203716879] = "AdvancedRifleClip01";
    WeaponComponentHash[WeaponComponentHash["AdvancedRifleClip02"] = 2395064697] = "AdvancedRifleClip02";
    WeaponComponentHash[WeaponComponentHash["AdvancedRifleVarmodLuxe"] = 930927479] = "AdvancedRifleVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["APPistolClip01"] = 834974250] = "APPistolClip01";
    WeaponComponentHash[WeaponComponentHash["APPistolClip02"] = 614078421] = "APPistolClip02";
    WeaponComponentHash[WeaponComponentHash["APPistolVarmodLuxe"] = 2608252716] = "APPistolVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleClip01"] = 3193891350] = "AssaultRifleClip01";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleClip02"] = 2971750299] = "AssaultRifleClip02";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleClip03"] = 3689981245] = "AssaultRifleClip03";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleVarmodLuxe"] = 1319990579] = "AssaultRifleVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["AssaultSMGClip01"] = 2366834608] = "AssaultSMGClip01";
    WeaponComponentHash[WeaponComponentHash["AssaultSMGClip02"] = 3141985303] = "AssaultSMGClip02";
    WeaponComponentHash[WeaponComponentHash["AssaultSMGVarmodLowrider"] = 663517359] = "AssaultSMGVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["AssaultShotgunClip01"] = 2498239431] = "AssaultShotgunClip01";
    WeaponComponentHash[WeaponComponentHash["AssaultShotgunClip02"] = 2260565874] = "AssaultShotgunClip02";
    WeaponComponentHash[WeaponComponentHash["AtArAfGrip"] = 202788691] = "AtArAfGrip";
    WeaponComponentHash[WeaponComponentHash["AtArFlsh"] = 2076495324] = "AtArFlsh";
    WeaponComponentHash[WeaponComponentHash["AtArSupp"] = 2205435306] = "AtArSupp";
    WeaponComponentHash[WeaponComponentHash["AtArSupp02"] = 2805810788] = "AtArSupp02";
    WeaponComponentHash[WeaponComponentHash["AtPiFlsh"] = 899381934] = "AtPiFlsh";
    WeaponComponentHash[WeaponComponentHash["AtPiSupp"] = 3271853210] = "AtPiSupp";
    WeaponComponentHash[WeaponComponentHash["AtPiSupp02"] = 1709866683] = "AtPiSupp02";
    WeaponComponentHash[WeaponComponentHash["AtRailCover01"] = 1967214384] = "AtRailCover01";
    WeaponComponentHash[WeaponComponentHash["AtScopeLarge"] = 3527687644] = "AtScopeLarge";
    WeaponComponentHash[WeaponComponentHash["AtScopeLargeFixedZoom"] = 471997210] = "AtScopeLargeFixedZoom";
    WeaponComponentHash[WeaponComponentHash["AtScopeMacro"] = 2637152041] = "AtScopeMacro";
    WeaponComponentHash[WeaponComponentHash["AtScopeMacro02"] = 1019656791] = "AtScopeMacro02";
    WeaponComponentHash[WeaponComponentHash["AtScopeMax"] = 3159677559] = "AtScopeMax";
    WeaponComponentHash[WeaponComponentHash["AtScopeMedium"] = 2698550338] = "AtScopeMedium";
    WeaponComponentHash[WeaponComponentHash["AtScopeSmall"] = 2855028148] = "AtScopeSmall";
    WeaponComponentHash[WeaponComponentHash["AtScopeSmall02"] = 1006677997] = "AtScopeSmall02";
    WeaponComponentHash[WeaponComponentHash["AtSrSupp"] = 3859329886] = "AtSrSupp";
    WeaponComponentHash[WeaponComponentHash["BullpupRifleClip01"] = 3315675008] = "BullpupRifleClip01";
    WeaponComponentHash[WeaponComponentHash["BullpupRifleClip02"] = 3009973007] = "BullpupRifleClip02";
    WeaponComponentHash[WeaponComponentHash["BullpupRifleVarmodLow"] = 2824322168] = "BullpupRifleVarmodLow";
    WeaponComponentHash[WeaponComponentHash["BullpupShotgunClip01"] = 3377353998] = "BullpupShotgunClip01";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleClip01"] = 2680042476] = "CarbineRifleClip01";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleClip02"] = 2433783441] = "CarbineRifleClip02";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleClip03"] = 3127044405] = "CarbineRifleClip03";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleVarmodLuxe"] = 3634075224] = "CarbineRifleVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["CombatMGClip01"] = 3791631178] = "CombatMGClip01";
    WeaponComponentHash[WeaponComponentHash["CombatMGClip02"] = 3603274966] = "CombatMGClip02";
    WeaponComponentHash[WeaponComponentHash["CombatMGVarmodLowrider"] = 2466172125] = "CombatMGVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["CombatPDWClip01"] = 1125642654] = "CombatPDWClip01";
    WeaponComponentHash[WeaponComponentHash["CombatPDWClip02"] = 860508675] = "CombatPDWClip02";
    WeaponComponentHash[WeaponComponentHash["CombatPDWClip03"] = 1857603803] = "CombatPDWClip03";
    WeaponComponentHash[WeaponComponentHash["CombatPistolClip01"] = 119648377] = "CombatPistolClip01";
    WeaponComponentHash[WeaponComponentHash["CombatPistolClip02"] = 3598405421] = "CombatPistolClip02";
    WeaponComponentHash[WeaponComponentHash["CombatPistolVarmodLowrider"] = 3328527730] = "CombatPistolVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["CompactRifleClip01"] = 1363085923] = "CompactRifleClip01";
    WeaponComponentHash[WeaponComponentHash["CompactRifleClip02"] = 1509923832] = "CompactRifleClip02";
    WeaponComponentHash[WeaponComponentHash["CompactRifleClip03"] = 3322377230] = "CompactRifleClip03";
    WeaponComponentHash[WeaponComponentHash["DBShotgunClip01"] = 703231006] = "DBShotgunClip01";
    WeaponComponentHash[WeaponComponentHash["FireworkClip01"] = 3840197261] = "FireworkClip01";
    WeaponComponentHash[WeaponComponentHash["FlareGunClip01"] = 2481569177] = "FlareGunClip01";
    WeaponComponentHash[WeaponComponentHash["FlashlightLight"] = 3719772431] = "FlashlightLight";
    WeaponComponentHash[WeaponComponentHash["GrenadeLauncherClip01"] = 296639639] = "GrenadeLauncherClip01";
    WeaponComponentHash[WeaponComponentHash["GusenbergClip01"] = 484812453] = "GusenbergClip01";
    WeaponComponentHash[WeaponComponentHash["GusenbergClip02"] = 3939025520] = "GusenbergClip02";
    WeaponComponentHash[WeaponComponentHash["HeavyPistolClip01"] = 222992026] = "HeavyPistolClip01";
    WeaponComponentHash[WeaponComponentHash["HeavyPistolClip02"] = 1694090795] = "HeavyPistolClip02";
    WeaponComponentHash[WeaponComponentHash["HeavyPistolVarmodLuxe"] = 2053798779] = "HeavyPistolVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["HeavyShotgunClip01"] = 844049759] = "HeavyShotgunClip01";
    WeaponComponentHash[WeaponComponentHash["HeavyShotgunClip02"] = 2535257853] = "HeavyShotgunClip02";
    WeaponComponentHash[WeaponComponentHash["HeavyShotgunClip03"] = 2294798931] = "HeavyShotgunClip03";
    WeaponComponentHash[WeaponComponentHash["HeavySniperClip01"] = 1198478068] = "HeavySniperClip01";
    WeaponComponentHash[WeaponComponentHash["HomingLauncherClip01"] = 4162006335] = "HomingLauncherClip01";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodBallas"] = 4007263587] = "KnuckleVarmodBallas";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodBase"] = 4081463091] = "KnuckleVarmodBase";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodDiamond"] = 2539772380] = "KnuckleVarmodDiamond";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodDollar"] = 1351683121] = "KnuckleVarmodDollar";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodHate"] = 2112683568] = "KnuckleVarmodHate";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodKing"] = 3800804335] = "KnuckleVarmodKing";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodLove"] = 1062111910] = "KnuckleVarmodLove";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodPimp"] = 3323197061] = "KnuckleVarmodPimp";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodPlayer"] = 146278587] = "KnuckleVarmodPlayer";
    WeaponComponentHash[WeaponComponentHash["KnuckleVarmodVagos"] = 2062808965] = "KnuckleVarmodVagos";
    WeaponComponentHash[WeaponComponentHash["MGClip01"] = 4097109892] = "MGClip01";
    WeaponComponentHash[WeaponComponentHash["MGClip02"] = **********] = "MGClip02";
    WeaponComponentHash[WeaponComponentHash["MGVarmodLowrider"] = **********] = "MGVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["MachinePistolClip01"] = **********] = "MachinePistolClip01";
    WeaponComponentHash[WeaponComponentHash["MachinePistolClip02"] = **********] = "MachinePistolClip02";
    WeaponComponentHash[WeaponComponentHash["MachinePistolClip03"] = **********] = "MachinePistolClip03";
    WeaponComponentHash[WeaponComponentHash["MarksmanPistolClip01"] = **********] = "MarksmanPistolClip01";
    WeaponComponentHash[WeaponComponentHash["MarksmanRifleClip01"] = **********] = "MarksmanRifleClip01";
    WeaponComponentHash[WeaponComponentHash["MarksmanRifleClip02"] = **********] = "MarksmanRifleClip02";
    WeaponComponentHash[WeaponComponentHash["MarksmanRifleVarmodLuxe"] = 371102273] = "MarksmanRifleVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["MicroSMGClip01"] = **********] = "MicroSMGClip01";
    WeaponComponentHash[WeaponComponentHash["MicroSMGClip02"] = 283556395] = "MicroSMGClip02";
    WeaponComponentHash[WeaponComponentHash["MicroSMGVarmodLuxe"] = **********] = "MicroSMGVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["MinigunClip01"] = **********] = "MinigunClip01";
    WeaponComponentHash[WeaponComponentHash["MusketClip01"] = **********] = "MusketClip01";
    WeaponComponentHash[WeaponComponentHash["Pistol50Clip01"] = 580369945] = "Pistol50Clip01";
    WeaponComponentHash[WeaponComponentHash["Pistol50Clip02"] = **********] = "Pistol50Clip02";
    WeaponComponentHash[WeaponComponentHash["Pistol50VarmodLuxe"] = 2008591151] = "Pistol50VarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["PistolClip01"] = 4275109233] = "PistolClip01";
    WeaponComponentHash[WeaponComponentHash["PistolClip02"] = 3978713628] = "PistolClip02";
    WeaponComponentHash[WeaponComponentHash["PistolVarmodLuxe"] = 3610841222] = "PistolVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["PoliceTorchFlashlight"] = 3315797997] = "PoliceTorchFlashlight";
    WeaponComponentHash[WeaponComponentHash["PumpShotgunClip01"] = 3513717816] = "PumpShotgunClip01";
    WeaponComponentHash[WeaponComponentHash["PumpShotgunVarmodLowrider"] = 2732039643] = "PumpShotgunVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["RPGClip01"] = 1319465907] = "RPGClip01";
    WeaponComponentHash[WeaponComponentHash["RailgunClip01"] = 59044840] = "RailgunClip01";
    WeaponComponentHash[WeaponComponentHash["RevolverClip01"] = 3917905123] = "RevolverClip01";
    WeaponComponentHash[WeaponComponentHash["RevolverVarmodBoss"] = 384708672] = "RevolverVarmodBoss";
    WeaponComponentHash[WeaponComponentHash["RevolverVarmodGoon"] = 2492708877] = "RevolverVarmodGoon";
    WeaponComponentHash[WeaponComponentHash["SMGClip01"] = 643254679] = "SMGClip01";
    WeaponComponentHash[WeaponComponentHash["SMGClip02"] = 889808635] = "SMGClip02";
    WeaponComponentHash[WeaponComponentHash["SMGClip03"] = 2043113590] = "SMGClip03";
    WeaponComponentHash[WeaponComponentHash["SMGVarmodLuxe"] = 663170192] = "SMGVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["SNSPistolClip01"] = 4169150169] = "SNSPistolClip01";
    WeaponComponentHash[WeaponComponentHash["SNSPistolClip02"] = 2063610803] = "SNSPistolClip02";
    WeaponComponentHash[WeaponComponentHash["SNSPistolVarmodLowrider"] = 2150886575] = "SNSPistolVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["SawnoffShotgunClip01"] = 3352699429] = "SawnoffShotgunClip01";
    WeaponComponentHash[WeaponComponentHash["SawnoffShotgunVarmodLuxe"] = 2242268665] = "SawnoffShotgunVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["SniperRifleClip01"] = 2613461129] = "SniperRifleClip01";
    WeaponComponentHash[WeaponComponentHash["SniperRifleVarmodLuxe"] = 1077065191] = "SniperRifleVarmodLuxe";
    WeaponComponentHash[WeaponComponentHash["SpecialCarbineClip01"] = 3334989185] = "SpecialCarbineClip01";
    WeaponComponentHash[WeaponComponentHash["SpecialCarbineClip02"] = 2089537806] = "SpecialCarbineClip02";
    WeaponComponentHash[WeaponComponentHash["SpecialCarbineClip03"] = 1801039530] = "SpecialCarbineClip03";
    WeaponComponentHash[WeaponComponentHash["SpecialCarbineVarmodLowrider"] = 1929467122] = "SpecialCarbineVarmodLowrider";
    WeaponComponentHash[WeaponComponentHash["SwitchbladeVarmodBase"] = 2436343040] = "SwitchbladeVarmodBase";
    WeaponComponentHash[WeaponComponentHash["SwitchbladeVarmodVar1"] = 1530822070] = "SwitchbladeVarmodVar1";
    WeaponComponentHash[WeaponComponentHash["SwitchbladeVarmodVar2"] = 3885209186] = "SwitchbladeVarmodVar2";
    WeaponComponentHash[WeaponComponentHash["VintagePistolClip01"] = 1168357051] = "VintagePistolClip01";
    WeaponComponentHash[WeaponComponentHash["VintagePistolClip02"] = 867832552] = "VintagePistolClip02";
    // mpgunrunning
    WeaponComponentHash[WeaponComponentHash["AtSights"] = 1108334355] = "AtSights";
    WeaponComponentHash[WeaponComponentHash["AtScopeSmallMk2"] = 1060929921] = "AtScopeSmallMk2";
    WeaponComponentHash[WeaponComponentHash["AtScopeMacroMk2"] = 77277509] = "AtScopeMacroMk2";
    WeaponComponentHash[WeaponComponentHash["AtScopeMediumMk2"] = 3328927042] = "AtScopeMediumMk2";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle1"] = 3113485012] = "AtMuzzle1";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle2"] = 3362234491] = "AtMuzzle2";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle3"] = 3725708239] = "AtMuzzle3";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle4"] = 3968886988] = "AtMuzzle4";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle5"] = 48731514] = "AtMuzzle5";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle6"] = 880736428] = "AtMuzzle6";
    WeaponComponentHash[WeaponComponentHash["AtMuzzle7"] = 1303784126] = "AtMuzzle7";
    WeaponComponentHash[WeaponComponentHash["AtArAfGrip2"] = 2640679034] = "AtArAfGrip2";
    //
    // PistolMk2
    WeaponComponentHash[WeaponComponentHash["PistolMk2ClipNormal"] = 2499030370] = "PistolMk2ClipNormal";
    WeaponComponentHash[WeaponComponentHash["PistolMk2ClipExtended"] = 1591132456] = "PistolMk2ClipExtended";
    WeaponComponentHash[WeaponComponentHash["PistolMk2ClipFMJ"] = 1329061674] = "PistolMk2ClipFMJ";
    WeaponComponentHash[WeaponComponentHash["PistolMk2ClipHollowpoint"] = 2248057097] = "PistolMk2ClipHollowpoint";
    WeaponComponentHash[WeaponComponentHash["PistolMk2ClipIncendiary"] = 733837882] = "PistolMk2ClipIncendiary";
    WeaponComponentHash[WeaponComponentHash["PistolMk2ClipTracer"] = 634039983] = "PistolMk2ClipTracer";
    WeaponComponentHash[WeaponComponentHash["PistolMk2Scope"] = 2396306288] = "PistolMk2Scope";
    WeaponComponentHash[WeaponComponentHash["PistolMk2Flash"] = 1140676955] = "PistolMk2Flash";
    WeaponComponentHash[WeaponComponentHash["PistolMk2Compensator"] = 568543123] = "PistolMk2Compensator";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoDigital"] = 1550611612] = "PistolMk2CamoDigital";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoBrushstroke"] = 368550800] = "PistolMk2CamoBrushstroke";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoWoodland"] = 2525897947] = "PistolMk2CamoWoodland";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSkull"] = 24902297] = "PistolMk2CamoSkull";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSessanta"] = 4066925682] = "PistolMk2CamoSessanta";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoPerseus"] = 3710005734] = "PistolMk2CamoPerseus";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoLeopard"] = 3141791350] = "PistolMk2CamoLeopard";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoZebra"] = 1301287696] = "PistolMk2CamoZebra";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoGeometric"] = 1597093459] = "PistolMk2CamoGeometric";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoBoom"] = 1769871776] = "PistolMk2CamoBoom";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoPatriotic"] = 2467084625] = "PistolMk2CamoPatriotic";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideDigital"] = 3036451504] = "PistolMk2CamoSlideDigital";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideBrushstroke"] = 438243936] = "PistolMk2CamoSlideBrushstroke";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideWoodland"] = 3839888240] = "PistolMk2CamoSlideWoodland";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideSkull"] = 740920107] = "PistolMk2CamoSlideSkull";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideSessanta"] = 3753350949] = "PistolMk2CamoSlideSessanta";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlidePerseus"] = 1809261196] = "PistolMk2CamoSlidePerseus";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideLeopard"] = 2648428428] = "PistolMk2CamoSlideLeopard";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideZebra"] = 3004802348] = "PistolMk2CamoSlideZebra";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideGeometric"] = 3330502162] = "PistolMk2CamoSlideGeometric";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlideBoom"] = 1135718771] = "PistolMk2CamoSlideBoom";
    WeaponComponentHash[WeaponComponentHash["PistolMk2CamoSlidePatriotic"] = 1253942266] = "PistolMk2CamoSlidePatriotic";
    //
    // AssaultRifleMk2
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2ClipNormal"] = 2249208895] = "AssaultRifleMk2ClipNormal";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2ClipExtended"] = 3509242479] = "AssaultRifleMk2ClipExtended";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2ClipArmorPiercing"] = 2816286296] = "AssaultRifleMk2ClipArmorPiercing";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2ClipFMJ"] = 1675665560] = "AssaultRifleMk2ClipFMJ";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2ClipIncendiary"] = 4218476627] = "AssaultRifleMk2ClipIncendiary";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2ClipTracer"] = 4012669121] = "AssaultRifleMk2ClipTracer";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2BarrelNormal"] = 1134861606] = "AssaultRifleMk2BarrelNormal";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2BarrelHeavy"] = 1447477866] = "AssaultRifleMk2BarrelHeavy";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoDigital"] = 2434475183] = "AssaultRifleMk2CamoDigital";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoBrushstroke"] = 937772107] = "AssaultRifleMk2CamoBrushstroke";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoWoodland"] = 1401650071] = "AssaultRifleMk2CamoWoodland";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoSkull"] = 628662130] = "AssaultRifleMk2CamoSkull";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoSessanta"] = 3309920045] = "AssaultRifleMk2CamoSessanta";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoPerseus"] = 3482022833] = "AssaultRifleMk2CamoPerseus";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoLeopard"] = 2847614993] = "AssaultRifleMk2CamoLeopard";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoZebra"] = 4234628436] = "AssaultRifleMk2CamoZebra";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoGeometric"] = 2088750491] = "AssaultRifleMk2CamoGeometric";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoBoom"] = 2781053842] = "AssaultRifleMk2CamoBoom";
    WeaponComponentHash[WeaponComponentHash["AssaultRifleMk2CamoPatriotic"] = 3115408816] = "AssaultRifleMk2CamoPatriotic";
    //
    // CarbineRifleMk2
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2ClipNormal"] = 1283078430] = "CarbineRifleMk2ClipNormal";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2ClipExtended"] = 1574296533] = "CarbineRifleMk2ClipExtended";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2ClipArmorPiercing"] = 626875735] = "CarbineRifleMk2ClipArmorPiercing";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2ClipFMJ"] = 1141059345] = "CarbineRifleMk2ClipFMJ";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2ClipIncendiary"] = 1025884839] = "CarbineRifleMk2ClipIncendiary";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2ClipTracer"] = 391640422] = "CarbineRifleMk2ClipTracer";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2BarrelNormal"] = 2201368575] = "CarbineRifleMk2BarrelNormal";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2BarrelHeavy"] = 2335983627] = "CarbineRifleMk2BarrelHeavy";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoDigital"] = 1272803094] = "CarbineRifleMk2CamoDigital";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoBrushstroke"] = 1080719624] = "CarbineRifleMk2CamoBrushstroke";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoWoodland"] = 792221348] = "CarbineRifleMk2CamoWoodland";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoSkull"] = 3842785869] = "CarbineRifleMk2CamoSkull";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoSessanta"] = 3548192559] = "CarbineRifleMk2CamoSessanta";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoPerseus"] = 2250671235] = "CarbineRifleMk2CamoPerseus";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoLeopard"] = 4095795318] = "CarbineRifleMk2CamoLeopard";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoZebra"] = 2866892280] = "CarbineRifleMk2CamoZebra";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoGeometric"] = 2559813981] = "CarbineRifleMk2CamoGeometric";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoBoom"] = 1796459838] = "CarbineRifleMk2CamoBoom";
    WeaponComponentHash[WeaponComponentHash["CarbineRifleMk2CamoPatriotic"] = 3663056191] = "CarbineRifleMk2CamoPatriotic";
    //
    // CombatMGMk2
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2ClipNormal"] = 1227564412] = "CombatMGMk2ClipNormal";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2ClipExtended"] = 400507625] = "CombatMGMk2ClipExtended";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2ClipArmorPiercing"] = 696788003] = "CombatMGMk2ClipArmorPiercing";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2ClipFMJ"] = 1475288264] = "CombatMGMk2ClipFMJ";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2ClipIncendiary"] = 3274096058] = "CombatMGMk2ClipIncendiary";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2ClipTracer"] = 4133787461] = "CombatMGMk2ClipTracer";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2BarrelNormal"] = 3276730932] = "CombatMGMk2BarrelNormal";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2BarrelHeavy"] = 3051509595] = "CombatMGMk2BarrelHeavy";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoDigital"] = 1249283253] = "CombatMGMk2CamoDigital";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoBrushstroke"] = 3437259709] = "CombatMGMk2CamoBrushstroke";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoWoodland"] = 3197423398] = "CombatMGMk2CamoWoodland";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoSkull"] = 1980349969] = "CombatMGMk2CamoSkull";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoSessanta"] = 1219453777] = "CombatMGMk2CamoSessanta";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoPerseus"] = 2441508106] = "CombatMGMk2CamoPerseus";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoLeopard"] = 2220186280] = "CombatMGMk2CamoLeopard";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoZebra"] = 457967755] = "CombatMGMk2CamoZebra";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoGeometric"] = 235171324] = "CombatMGMk2CamoGeometric";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoBoom"] = 42685294] = "CombatMGMk2CamoBoom";
    WeaponComponentHash[WeaponComponentHash["CombatMGMk2CamoPatriotic"] = 3607349581] = "CombatMGMk2CamoPatriotic";
    //
    // HeavySniperMk2
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ClipNormal"] = 4196276776] = "HeavySniperMk2ClipNormal";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ClipExtended"] = 752418717] = "HeavySniperMk2ClipExtended";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ClipArmorPiercing"] = 4164277972] = "HeavySniperMk2ClipArmorPiercing";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ClipExplosive"] = 2313935527] = "HeavySniperMk2ClipExplosive";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ClipFMJ"] = 1005144310] = "HeavySniperMk2ClipFMJ";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ClipIncendiary"] = 247526935] = "HeavySniperMk2ClipIncendiary";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ScopeLarge"] = 2193687427] = "HeavySniperMk2ScopeLarge";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ScopeNightvision"] = 3061846192] = "HeavySniperMk2ScopeNightvision";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2ScopeThermal"] = 776198721] = "HeavySniperMk2ScopeThermal";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2Suppressor"] = 2890063729] = "HeavySniperMk2Suppressor";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2Muzzle8"] = 1602080333] = "HeavySniperMk2Muzzle8";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2Muzzle9"] = 1764221345] = "HeavySniperMk2Muzzle9";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2BarrelNormal"] = 2425761975] = "HeavySniperMk2BarrelNormal";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2BarrelHeavy"] = 277524638] = "HeavySniperMk2BarrelHeavy";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoDigital"] = 4164123906] = "HeavySniperMk2CamoDigital";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoBrushstroke"] = 3317620069] = "HeavySniperMk2CamoBrushstroke";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoWoodland"] = 3916506229] = "HeavySniperMk2CamoWoodland";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoSkull"] = 329939175] = "HeavySniperMk2CamoSkull";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoSessanta"] = 643374672] = "HeavySniperMk2CamoSessanta";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoPerseus"] = 807875052] = "HeavySniperMk2CamoPerseus";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoLeopard"] = 2893163128] = "HeavySniperMk2CamoLeopard";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoZebra"] = 3198471901] = "HeavySniperMk2CamoZebra";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoGeometric"] = 3447155842] = "HeavySniperMk2CamoGeometric";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoBoom"] = 2881858759] = "HeavySniperMk2CamoBoom";
    WeaponComponentHash[WeaponComponentHash["HeavySniperMk2CamoPatriotic"] = 1815270123] = "HeavySniperMk2CamoPatriotic";
    //
    // SMGMk2
    WeaponComponentHash[WeaponComponentHash["SMGMk2ClipNormal"] = 1277460590] = "SMGMk2ClipNormal";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ClipExtended"] = 3112393518] = "SMGMk2ClipExtended";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ClipFMJ"] = 190476639] = "SMGMk2ClipFMJ";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ClipHollowpoint"] = 974903034] = "SMGMk2ClipHollowpoint";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ClipIncendiary"] = 3650233061] = "SMGMk2ClipIncendiary";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ClipTracer"] = 2146055916] = "SMGMk2ClipTracer";
    WeaponComponentHash[WeaponComponentHash["SMGMk2Sights"] = 2681951826] = "SMGMk2Sights";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ScopeMacro"] = 3842157419] = "SMGMk2ScopeMacro";
    WeaponComponentHash[WeaponComponentHash["SMGMk2ScopeSmall"] = 1038927834] = "SMGMk2ScopeSmall";
    WeaponComponentHash[WeaponComponentHash["SMGMk2BarrelNormal"] = 3641720545] = "SMGMk2BarrelNormal";
    WeaponComponentHash[WeaponComponentHash["SMGMk2BarrelHeavy"] = 2774849419] = "SMGMk2BarrelHeavy";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoDigital"] = 3298267239] = "SMGMk2CamoDigital";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoBrushstroke"] = 940943685] = "SMGMk2CamoBrushstroke";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoWoodland"] = 1263226800] = "SMGMk2CamoWoodland";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoSkull"] = 3966931456] = "SMGMk2CamoSkull";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoSessanta"] = 1224100642] = "SMGMk2CamoSessanta";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoPerseus"] = 899228776] = "SMGMk2CamoPerseus";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoLeopard"] = 616006309] = "SMGMk2CamoLeopard";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoZebra"] = 2733014785] = "SMGMk2CamoZebra";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoGeometric"] = 572063080] = "SMGMk2CamoGeometric";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoBoom"] = 1170588613] = "SMGMk2CamoBoom";
    WeaponComponentHash[WeaponComponentHash["SMGMk2CamoPatriotic"] = 966612367] = "SMGMk2CamoPatriotic";
    //
})(WeaponComponentHash || (WeaponComponentHash = {}));
