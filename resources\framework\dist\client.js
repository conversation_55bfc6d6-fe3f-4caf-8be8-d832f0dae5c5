(() => {
  // client/functions.ts
  var DELAY = (ms) => new Promise((res) => setTimeout(res, ms, 0));

  // client/spawn.ts
  var spawnPoints = [];
  var autoSpawnEnabled = false;
  var spawnLock = false;
  var respawnForced = false;
  var spawnNum = 1;
  on("getMapDirectives", (add) => {
    add("spawnpoint", (state, model) => {
      return (opts) => {
        try {
          const x = (opts.x || opts[0]) + 1e-4;
          const y = (opts.y || opts[1]) + 1e-4;
          const z = (opts.z || opts[2]) + 1e-4;
          const heading = (opts.heading || 0) + 0.01;
          const modelHash = typeof model === "string" ? GetHashKey(model) : model;
          const spawnPoint = { x, y, z, heading, model: modelHash };
          const spawnIdx = addSpawnPoint(spawnPoint);
          if (spawnIdx) {
            state.add("spawnIdx", spawnIdx);
          }
        } catch (e) {
          console.error(`[spawnmanager] Error processing spawnpoint directive: ${e}`);
        }
      };
    }, (state) => {
      if (state.spawnIdx) {
        removeSpawnPoint(state.spawnIdx);
      }
    });
  });
  function addSpawnPoint(spawn) {
    try {
      if (typeof spawn.x !== "number" || typeof spawn.y !== "number" || typeof spawn.z !== "number" || typeof spawn.heading !== "number") {
        throw new Error("Invalid spawn data: x, y, z, and heading must be numbers.");
      }
      let model = spawn.model;
      if (typeof model === "string") {
        model = GetHashKey(model);
      }
      if (!IsModelInCdimage(model)) {
        throw new Error(`Invalid spawn model: ${spawn.model}`);
      }
      spawn.model = model;
      spawn.idx = spawnNum++;
      spawnPoints.push(spawn);
      return spawn.idx;
    } catch (e) {
      console.error(`[spawnmanager] Could not add spawn point: ${e}`);
    }
  }
  function removeSpawnPoint(spawnIdx) {
    const index = spawnPoints.findIndex((sp) => sp.idx === spawnIdx);
    if (index > -1) {
      spawnPoints.splice(index, 1);
    }
  }
  function setAutoSpawn(enabled) {
    autoSpawnEnabled = enabled;
  }
  function forceRespawn() {
    spawnLock = false;
    respawnForced = true;
  }

  // client/client.ts
  var DEFAULT_SPAWN_LOCATION = { x: -1612.473, y: 761.133, z: 189.242, heading: 293.613 };
  onNet("framework:user_ready", () => {
    ShutdownLoadingScreen();
    emit("framework:toggleTargeting", true);
    setAutoSpawn(false);
    SetNuiFocus(false, false);
    const [foundGround, groundZ] = GetGroundZFor_3dCoord(DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, false);
    if (foundGround) {
      DEFAULT_SPAWN_LOCATION.z = groundZ;
    }
    SetEntityCoords(PlayerPedId(), DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, true, false, false, true);
    SetEntityHeading(PlayerPedId(), DEFAULT_SPAWN_LOCATION.heading);
    SetEntityVisible(PlayerId(), false, false);
  });
  onNet("framework:showCharacterSelection", (characters) => {
    SetNuiFocus(true, true);
    SendNuiMessage(JSON.stringify({
      action: "showCharacterSelection",
      characters: Array.isArray(characters) ? characters : []
    }));
  });
  onNet("framework:spawnPlayer", () => {
    console.log("Spawning player as requested by server.");
    setAutoSpawn(true);
    forceRespawn();
  });
  RegisterNuiCallback("uiReady", (data, cb) => {
    emitNet("framework:server_gather");
    cb({ ok: true });
  });
  RegisterNuiCallback("selectCharacter", async (data, cb) => {
    console.log("Character selected:", data);
    emitNet("framework:characterSelected", data);
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: "hideCharacterSelection" }));
    const ped = PlayerPedId();
    const gender = data.gender;
    const model = gender === "male" ? "mp_m_freemode_01" : "mp_f_freemode_01";
    RequestModel(model);
    while (!HasModelLoaded(model)) {
      console.log("Waiting for model to load...");
      await DELAY(0);
    }
    console.log("Model loaded:", model);
    ClearPedTasksImmediately(ped);
    RemoveAllPedWeapons(ped, true);
    ClearPlayerWantedLevel(PlayerId());
    FreezeEntityPosition(ped, false);
    SetPlayerModel(ped, model);
    SetModelAsNoLongerNeeded(model);
    SetEntityVisible(ped, true, false);
    SetEntityCollision(ped, true, true);
    cb({ ok: true });
  });
  RegisterNuiCallback("submitNewCharacter", (data, cb) => {
    emitNet("framework:createCharacter", data);
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: "hideCharacterSelection" }));
    cb({ ok: true });
  });
  RegisterNuiCallback("deleteCharacter", (data, cb) => {
    console.log("Requesting to delete character:", data);
    emitNet("framework:server_deleteCharacter", data);
    SetNuiFocus(false, false);
    SendNuiMessage(JSON.stringify({ action: "hideCharacterSelection" }));
    cb({ ok: true });
  });
  RegisterNuiCallback("createCharacter", (data, cb) => {
    SendNuiMessage(JSON.stringify({ action: "showCreateForm" }));
    cb({ ok: true });
  });
})();
