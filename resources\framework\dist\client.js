(() => {
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __esm = (fn, res) => function __init() {
    return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
  };
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };

  // client/functions.ts
  var DELAY;
  var init_functions = __esm({
    "client/functions.ts"() {
      DELAY = (ms) => new Promise((res) => setTimeout(res, ms, 0));
    }
  });

  // client/spawn.ts
  function addSpawnPoint(spawn) {
    try {
      if (typeof spawn.x !== "number" || typeof spawn.y !== "number" || typeof spawn.z !== "number" || typeof spawn.heading !== "number") {
        throw new Error("Invalid spawn data: x, y, z, and heading must be numbers.");
      }
      let model = spawn.model;
      if (typeof model === "string") {
        model = GetHashKey(model);
      }
      if (!IsModelInCdimage(model)) {
        throw new Error(`Invalid spawn model: ${spawn.model}`);
      }
      spawn.model = model;
      spawn.idx = spawnNum++;
      spawnPoints.push(spawn);
      return spawn.idx;
    } catch (e) {
      console.error(`[spawnmanager] Could not add spawn point: ${e}`);
    }
  }
  function removeSpawnPoint(spawnIdx) {
    const index = spawnPoints.findIndex((sp) => sp.idx === spawnIdx);
    if (index > -1) {
      spawnPoints.splice(index, 1);
    }
  }
  function setAutoSpawn(enabled) {
    autoSpawnEnabled = enabled;
  }
  function forceRespawn() {
    spawnLock = false;
    respawnForced = true;
  }
  var spawnPoints, autoSpawnEnabled, spawnLock, respawnForced, spawnNum;
  var init_spawn = __esm({
    "client/spawn.ts"() {
      init_functions();
      spawnPoints = [];
      autoSpawnEnabled = false;
      spawnLock = false;
      respawnForced = false;
      spawnNum = 1;
      on("getMapDirectives", (add) => {
        add("spawnpoint", (state, model) => {
          return (opts) => {
            try {
              const x = (opts.x || opts[0]) + 1e-4;
              const y = (opts.y || opts[1]) + 1e-4;
              const z = (opts.z || opts[2]) + 1e-4;
              const heading = (opts.heading || 0) + 0.01;
              const modelHash = typeof model === "string" ? GetHashKey(model) : model;
              const spawnPoint = { x, y, z, heading, model: modelHash };
              const spawnIdx = addSpawnPoint(spawnPoint);
              if (spawnIdx) {
                state.add("spawnIdx", spawnIdx);
              }
            } catch (e) {
              console.error(`[spawnmanager] Error processing spawnpoint directive: ${e}`);
            }
          };
        }, (state) => {
          if (state.spawnIdx) {
            removeSpawnPoint(state.spawnIdx);
          }
        });
      });
    }
  });

  // client/client.ts
  var require_client = __commonJS({
    "client/client.ts"(exports) {
      init_functions();
      init_spawn();
      var exp = global.exports;
      var currentCharacter = null;
      var DEFAULT_SPAWN_LOCATION = { x: -1612.473, y: 761.133, z: 189.242, heading: 328.646 };
      onNet("framework:user_ready", () => {
        const ped = PlayerPedId();
        SetEntityVisible(ped, false, false);
        ShutdownLoadingScreen();
        emit("framework:toggleTargeting", true);
        setAutoSpawn(false);
        SetNuiFocus(false, false);
        const [foundGround, groundZ] = GetGroundZFor_3dCoord(DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, false);
        if (foundGround) {
          DEFAULT_SPAWN_LOCATION.z = groundZ;
        }
        SetEntityCoords(ped, DEFAULT_SPAWN_LOCATION.x, DEFAULT_SPAWN_LOCATION.y, DEFAULT_SPAWN_LOCATION.z, true, false, false, true);
        SetEntityHeading(ped, DEFAULT_SPAWN_LOCATION.heading);
      });
      onNet("framework:showCharacterSelection", (characters) => {
        SetNuiFocus(true, true);
        SendNuiMessage(JSON.stringify({
          action: "showCharacterSelection",
          characters: Array.isArray(characters) ? characters : []
        }));
      });
      onNet("framework:spawnPlayer", () => {
        console.log("Spawning player as requested by server.");
        setAutoSpawn(true);
        forceRespawn();
      });
      RegisterNuiCallback("uiReady", (data, cb) => {
        emitNet("framework:server_gather");
        cb({ ok: true });
      });
      RegisterNuiCallback("selectCharacter", async (character, cb) => {
        currentCharacter = character;
        emitNet("framework:characterSelected", character);
        SetNuiFocus(false, false);
        SendNuiMessage(JSON.stringify({ action: "hideCharacterSelection" }));
        const model = character.appearance.model || (character.gender === "male" ? "mp_m_freemode_01" : "mp_f_freemode_01");
        await setPlayerModel(model);
        const ped = PlayerPedId();
        if (!character.appearance || !character.appearance.components || character.appearance.components.length === 0) {
          exp["fivem-appearance"].startPlayerCustomization((appearance) => {
            if (appearance) {
              console.log("Customization saved");
              emitNet("framework:saveAppearanceData", character.id, JSON.stringify(appearance));
            } else {
              console.log("Customization canceled, returning to selection.");
              emitNet("framework:server_gather");
            }
          });
        } else {
          exp["fivem-appearance"].setPlayerAppearance(character.appearance);
          const [foundGround, groundZ] = GetGroundZFor_3dCoord(character.position.x, character.position.y, character.position.z, false);
          if (foundGround) {
            character.position.z = groundZ;
          }
          SetEntityCoords(ped, character.position.x, character.position.y, character.position.z, true, false, false, true);
          SetEntityHeading(ped, character.position.heading);
        }
        ClearPedTasksImmediately(ped);
        RemoveAllPedWeapons(ped, true);
        ClearPlayerWantedLevel(PlayerId());
        FreezeEntityPosition(ped, false);
        SetEntityVisible(ped, true, false);
        SetEntityCollision(ped, true, true);
        emit("framework:toggleTargeting", false);
        cb({ ok: true });
      });
      async function setPlayerModel(model) {
        if (!model)
          return;
        if (!IsModelInCdimage(model))
          return;
        RequestModel(model);
        while (!HasModelLoaded(model)) {
          await DELAY(0);
        }
        SetPlayerModel(PlayerId(), model);
        SetModelAsNoLongerNeeded(model);
        const playerPed = PlayerPedId();
        if (model === "mp_m_freemode_01" || model === "mp_f_freemode_01") {
          SetPedDefaultComponentVariation(playerPed);
          SetPedHeadBlendData(playerPed, 0, 0, 0, 0, 0, 0, 0, 0, 0, false);
        }
      }
      RegisterNuiCallback("submitNewCharacter", (data, cb) => {
        emitNet("framework:createCharacter", data);
        SetNuiFocus(false, false);
        SendNuiMessage(JSON.stringify({ action: "hideCharacterSelection" }));
        cb({ ok: true });
      });
      RegisterNuiCallback("deleteCharacter", (data, cb) => {
        console.log("Requesting to delete character:", data);
        emitNet("framework:server_deleteCharacter", data);
        SetNuiFocus(false, false);
        SendNuiMessage(JSON.stringify({ action: "hideCharacterSelection" }));
        cb({ ok: true });
      });
      RegisterNuiCallback("createCharacter", (data, cb) => {
        SendNuiMessage(JSON.stringify({ action: "showCreateForm" }));
        cb({ ok: true });
      });
      exports("getCurrentCharacter", () => {
        return currentCharacter;
      });
    }
  });
  require_client();
})();
