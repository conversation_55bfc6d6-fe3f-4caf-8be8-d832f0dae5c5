export declare enum CheckpointIcon {
    CylinderSingleArrow = 0,
    <PERSON><PERSON>erDoubleArrow = 1,
    CylinderTripleArrow = 2,
    <PERSON>linderCycleArrow = 3,
    <PERSON>linderCheckerboard = 4,
    CylinderSingleArrow2 = 5,
    CylinderDoubleArrow2 = 6,
    <PERSON>linderTripleArrow2 = 7,
    <PERSON>linderCycleArrow2 = 8,
    <PERSON>linderCheckerboard2 = 9,
    RingSingleArrow = 10,
    RingDoubleArrow = 11,
    RingTripleArrow = 12,
    RingCycleArrow = 13,
    RingCheckerboard = 14,
    SingleArrow = 15,
    DoubleArrow = 16,
    TripleArrow = 17,
    CycleArrow = 18,
    Checkerboard = 19,
    CylinderSingleArrow3 = 20,
    CylinderDoubleArrow3 = 21,
    CylinderTripleArrow3 = 22,
    <PERSON>linderCycleArrow3 = 23,
    <PERSON>linderCheckerboard3 = 24,
    CylinderSingleArrow4 = 25,
    CylinderDoubleArrow4 = 26,
    CylinderTripleArrow4 = 27,
    CylinderCycleArrow4 = 28,
    <PERSON>linderCheckerboard4 = 29,
    CylinderSingleArrow5 = 30,
    CylinderDoubleArrow5 = 31,
    CylinderTripleArrow5 = 32,
    <PERSON><PERSON>erCycleArrow5 = 33,
    <PERSON>linderCheckerboard5 = 34,
    RingPlaneUp = 35,
    RingPlaneLeft = 36,
    RingPlaneRight = 37,
    RingPlaneDown = 38,
    Empty = 39,
    Ring = 40,
    Empty2 = 41,
    Cyclinder = 45,
    Cyclinder2 = 46,
    Cyclinder3 = 47
}
export declare enum CheckpointCustomIconStyle {
    Number = 0,
    SingleArrow = 1,
    DoubleArrow = 2,
    TripleArrow = 3,
    Ring = 4,
    CycleArrow = 5,
    Ring2 = 6,
    RingPointer = 7,
    SegmentedRing = 8,
    Sphere = 9,
    Dollar = 10,
    QuintupleLines = 11,
    BeastIcon = 12
}
