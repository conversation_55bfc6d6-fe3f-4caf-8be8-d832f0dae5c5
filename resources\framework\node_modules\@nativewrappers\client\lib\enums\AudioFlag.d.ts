export declare enum AudioFlag {
    ActivateSwitchWheelAudio = 0,
    AllowCutsceneOverScreenFade = 1,
    AllowForceRadioAfterRetune = 2,
    AllowPainAndAmbientSpeechToPlayDuringCutscene = 3,
    AllowPlayerAIOnMission = 4,
    AllowPoliceScannerWhenPlayerHasNoControl = 5,
    AllowRadioDuringSwitch = 6,
    AllowRadioOverScreenFade = 7,
    AllowScoreAndRadio = 8,
    AllowScriptedSpeechInSlowMo = 9,
    AvoidMissionCompleteDelay = 10,
    DisableAbortConversationForDeathAndInjury = 11,
    DisableAbortConversationForRagdoll = 12,
    DisableBarks = 13,
    DisableFlightMusic = 14,
    DisableReplayScriptStreamRecording = 15,
    EnableHeadsetBeep = 16,
    ForceConversationInterrupt = 17,
    ForceSeamlessRadioSwitch = 18,
    ForceSniperAudio = 19,
    FrontendRadioDisabled = 20,
    HoldMissionCompleteWhenPrepared = 21,
    IsDirectorModeActive = 22,
    IsPlayerOnMissionForSpeech = 23,
    ListenerReverbDisabled = 24,
    LoadMPData = 25,
    MobileRadioInGame = 26,
    OnlyAllowScriptTriggerPoliceScanner = 27,
    PlayMenuMusic = 28,
    PoliceScannerDisabled = 29,
    ScriptedConvListenerMaySpeak = 30,
    SpeechDucksScore = 31,
    SuppressPlayerScubaBreathing = 32,
    WantedMusicDisabled = 33,
    WantedMusicOnMission = 34
}
