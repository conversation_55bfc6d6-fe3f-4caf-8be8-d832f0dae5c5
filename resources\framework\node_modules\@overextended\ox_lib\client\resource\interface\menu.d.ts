import type { IconName, IconPrefix } from '@fortawesome/fontawesome-common-types';
type MenuPosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
type ChangeFunction = (selected: number, scrollIndex?: number, args?: any, checked?: boolean) => void;
type IconAnimation = 'spin' | 'spinPulse' | 'spinReverse' | 'pulse' | 'beat' | 'fade' | 'beatFade' | 'bounce' | 'shake';
interface MenuOptions {
    label: string;
    icon?: IconName | [IconPrefix, IconName] | string;
    iconColor?: string;
    iconAnimation?: IconAnimation;
    checked?: boolean;
    values?: Array<string | {
        label: string;
        description: string;
    }>;
    description?: string;
    defaultIndex?: number;
    args?: Record<any, any>;
    close?: boolean;
}
interface MenuProps {
    id: string;
    title: string;
    options: MenuOptions[];
    position?: MenuPosition;
    disableInput?: boolean;
    canClose?: boolean;
    onClose?: (keyPressed?: 'Escape' | 'Backspace') => void;
    onSelected?: ChangeFunction;
    onSideScroll?: ChangeFunction;
    onCheck?: ChangeFunction;
    cb?: ChangeFunction;
}
type registerMenu = (data: MenuProps, cb: ChangeFunction) => void;
export declare const registerMenu: registerMenu;
export declare const showMenu: (id: string) => string;
export declare const hideMenu: (onExit: boolean) => void;
export declare const getOpenMenu: () => string | null;
type setMenuOptions = (id: string, options: MenuOptions | MenuOptions[], index?: number) => void;
export declare const setMenuOptions: setMenuOptions;
export {};
