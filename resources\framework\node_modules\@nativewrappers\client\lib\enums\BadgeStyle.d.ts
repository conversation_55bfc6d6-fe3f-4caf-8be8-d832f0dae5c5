export declare enum BadgeStyle {
    None = 0,
    Lock = 1,
    Star = 2,
    Warning = 3,
    Crown = 4,
    MedalBronze = 5,
    MedalGold = 6,
    MedalSilver = 7,
    Cash = 8,
    <PERSON> = 9,
    <PERSON><PERSON> = 10,
    Meth = 11,
    Weed = 12,
    Ammo = 13,
    <PERSON><PERSON> = 14,
    <PERSON> = 15,
    <PERSON><PERSON><PERSON> = 16,
    <PERSON> = 17,
    <PERSON><PERSON> = 18,
    <PERSON> = 19,
    <PERSON> = 20,
    HealthHeart = 21,
    MakeupBrush = 22,
    <PERSON> = 23,
    <PERSON> = 24,
    <PERSON><PERSON><PERSON> = 25,
    Tick = 26,
    <PERSON> = 27,
    Female = 28,
    Male = 29,
    LockArena = 30,
    Adversary = 31,
    BaseJumping = 32,
    Briefcase = 33,
    MissionStar = 34,
    Deathmatch = 35,
    Castle = 36,
    Trophy = 37,
    RaceFlag = 38,
    RaceFlagPlane = 39,
    RaceFlagBicycle = 40,
    RaceFlagPerson = 41,
    RaceFlagCar = 42,
    RaceFlagBoatAnchor = 43,
    Rockstar = 44,
    Stunt = 45,
    StuntPremium = 46,
    RaceFlagStuntJump = 47,
    Shield = 48,
    TeamDeathmatch = 49,
    VehicleDeathmatch = 50,
    MpAmmoPickup = 51,
    MpAmmo = 52,
    MpCash = 53,
    MpRp = 54,
    MpSpectating = 55,
    Sale = 56,
    GlobeWhite = 57,
    GlobeRed = 58,
    GlobeBlue = 59,
    GlobeYellow = 60,
    GlobeGreen = 61,
    GlobeOrange = 62,
    InvArmWrestling = 63,
    InvBasejump = 64,
    InvMission = 65,
    InvDarts = 66,
    InvDeathmatch = 67,
    InvDrug = 68,
    InvCastle = 69,
    InvGolf = 70,
    InvBike = 71,
    InvBoat = 72,
    InvAnchor = 73,
    InvCar = 74,
    InvDollar = 75,
    InvCoke = 76,
    InvKey = 77,
    InvData = 78,
    InvHeli = 79,
    InvHeorin = 80,
    InvKeycard = 81,
    InvMeth = 82,
    InvBriefcase = 83,
    InvLink = 84,
    InvPerson = 85,
    InvPlane = 86,
    InvPlane2 = 87,
    InvQuestionmark = 88,
    InvRemote = 89,
    InvSafe = 90,
    InvSteerWheel = 91,
    InvWeapon = 92,
    InvWeed = 93,
    InvRaceFlagPlane = 94,
    InvRaceFlagBicycle = 95,
    InvRaceFlagBoatAnchor = 96,
    InvRaceFlagPerson = 97,
    InvRaceFlagCar = 98,
    InvRaceFlagHelmet = 99,
    InvShootingRange = 100,
    InvSurvival = 101,
    InvTeamDeathmatch = 102,
    InvTennis = 103,
    InvVehicleDeathmatch = 104,
    AudioMute = 105,
    AudioInactive = 106,
    AudioVol1 = 107,
    AudioVol2 = 108,
    AudioVol3 = 109,
    CountryUsa = 110,
    CountryUk = 111,
    CountrySweden = 112,
    CountryKorea = 113,
    CountryJapan = 114,
    CountryItaly = 115,
    CountryGermany = 116,
    CountryFrance = 117,
    BrandAlbany = 118,
    BrandAnnis = 119,
    BrandBanshee = 120,
    BrandBenefactor = 121,
    BrandBf = 122,
    BrandBollokan = 123,
    BrandBravado = 124,
    BrandBrute = 125,
    BrandBuckingham = 126,
    BrandCanis = 127,
    BrandChariot = 128,
    BrandCheval = 129,
    BrandClassique = 130,
    BrandCoil = 131,
    BrandDeclasse = 132,
    BrandDewbauchee = 133,
    BrandDilettante = 134,
    BrandDinka = 135,
    BrandDundreary = 136,
    BrandEmporer = 137,
    BrandEnus = 138,
    BrandFathom = 139,
    BrandGalivanter = 140,
    BrandGrotti = 141,
    BrandGrotti2 = 142,
    BrandHijak = 143,
    BrandHvy = 144,
    BrandImponte = 145,
    BrandInvetero = 146,
    BrandJacksheepe = 147,
    BrandLcc = 148,
    BrandJobuilt = 149,
    BrandKarin = 150,
    BrandLampadati = 151,
    BrandMaibatsu = 152,
    BrandMammoth = 153,
    BrandMtl = 154,
    BrandNagasaki = 155,
    BrandObey = 156,
    BrandOcelot = 157,
    BrandOverflod = 158,
    BrandPed = 159,
    BrandPegassi = 160,
    BrandPfister = 161,
    BrandPrincipe = 162,
    BrandProgen = 163,
    BrandProgen2 = 164,
    BrandRune = 165,
    BrandSchyster = 166,
    BrandShitzu = 167,
    BrandSpeedophile = 168,
    BrandStanley = 169,
    BrandTruffade = 170,
    BrandUbermacht = 171,
    BrandVapid = 172,
    BrandVulcar = 173,
    BrandWeeny = 174,
    BrandWestern = 175,
    BrandWesternmotorcycle = 176,
    BrandWillard = 177,
    BrandZirconium = 178,
    Info = 179
}
