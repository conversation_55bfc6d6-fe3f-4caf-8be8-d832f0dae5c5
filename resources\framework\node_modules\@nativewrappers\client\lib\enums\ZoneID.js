/**
 * List of Zones. Useful for using world zone related natives.
 */
export var ZoneID;
(function (ZoneID) {
    ZoneID[ZoneID["AIRP"] = 0] = "AIRP";
    ZoneID[ZoneID["ALAMO"] = 1] = "ALAMO";
    ZoneID[ZoneID["ALTA"] = 2] = "ALTA";
    ZoneID[ZoneID["ARMYB"] = 3] = "ARMYB";
    ZoneID[ZoneID["BANHAMC"] = 4] = "BANHAMC";
    ZoneID[ZoneID["BANNING"] = 5] = "BANNING";
    ZoneID[ZoneID["BEACH"] = 6] = "BEACH";
    ZoneID[ZoneID["BHAMCA"] = 7] = "BHAMCA";
    ZoneID[ZoneID["BRADP"] = 8] = "BRADP";
    ZoneID[ZoneID["BRADT"] = 9] = "BRADT";
    ZoneID[ZoneID["BURTON"] = 10] = "BURTON";
    ZoneID[ZoneID["CALAFB"] = 11] = "CALAFB";
    ZoneID[ZoneID["CANNY"] = 12] = "CANNY";
    ZoneID[ZoneID["CCREAK"] = 13] = "CCREAK";
    ZoneID[ZoneID["CHAMH"] = 14] = "CHAMH";
    ZoneID[ZoneID["CHIL"] = 15] = "CHIL";
    ZoneID[ZoneID["CHU"] = 16] = "CHU";
    ZoneID[ZoneID["CMSW"] = 17] = "CMSW";
    ZoneID[ZoneID["CYPRE"] = 18] = "CYPRE";
    ZoneID[ZoneID["DAVIS"] = 19] = "DAVIS";
    ZoneID[ZoneID["DELBE"] = 20] = "DELBE";
    ZoneID[ZoneID["DELPE"] = 21] = "DELPE";
    ZoneID[ZoneID["DELSOL"] = 22] = "DELSOL";
    ZoneID[ZoneID["DESRT"] = 23] = "DESRT";
    ZoneID[ZoneID["DOWNT"] = 24] = "DOWNT";
    ZoneID[ZoneID["DTVINE"] = 25] = "DTVINE";
    ZoneID[ZoneID["EAST_V"] = 26] = "EAST_V";
    ZoneID[ZoneID["EBURO"] = 27] = "EBURO";
    ZoneID[ZoneID["ELGORL"] = 28] = "ELGORL";
    ZoneID[ZoneID["ELYSIAN"] = 29] = "ELYSIAN";
    ZoneID[ZoneID["GALFISH"] = 30] = "GALFISH";
    ZoneID[ZoneID["golf"] = 31] = "golf";
    ZoneID[ZoneID["GRAPES"] = 32] = "GRAPES";
    ZoneID[ZoneID["GREATC"] = 33] = "GREATC";
    ZoneID[ZoneID["HARMO"] = 34] = "HARMO";
    ZoneID[ZoneID["HAWICK"] = 35] = "HAWICK";
    ZoneID[ZoneID["HORS"] = 36] = "HORS";
    ZoneID[ZoneID["HUMLAB"] = 37] = "HUMLAB";
    ZoneID[ZoneID["JAIL"] = 38] = "JAIL";
    ZoneID[ZoneID["KOREAT"] = 39] = "KOREAT";
    ZoneID[ZoneID["LACT"] = 40] = "LACT";
    ZoneID[ZoneID["LAGO"] = 41] = "LAGO";
    ZoneID[ZoneID["LDAM"] = 42] = "LDAM";
    ZoneID[ZoneID["LEGSQU"] = 43] = "LEGSQU";
    ZoneID[ZoneID["LMESA"] = 44] = "LMESA";
    ZoneID[ZoneID["LOSPUER"] = 45] = "LOSPUER";
    ZoneID[ZoneID["MIRR"] = 46] = "MIRR";
    ZoneID[ZoneID["MORN"] = 47] = "MORN";
    ZoneID[ZoneID["MOVIE"] = 48] = "MOVIE";
    ZoneID[ZoneID["MTCHIL"] = 49] = "MTCHIL";
    ZoneID[ZoneID["MTGORDO"] = 50] = "MTGORDO";
    ZoneID[ZoneID["MTJOSE"] = 51] = "MTJOSE";
    ZoneID[ZoneID["MURRI"] = 52] = "MURRI";
    ZoneID[ZoneID["NCHU"] = 53] = "NCHU";
    ZoneID[ZoneID["NOOSE"] = 54] = "NOOSE";
    ZoneID[ZoneID["OCEANA"] = 55] = "OCEANA";
    ZoneID[ZoneID["PALCOV"] = 56] = "PALCOV";
    ZoneID[ZoneID["PALETO"] = 57] = "PALETO";
    ZoneID[ZoneID["PALFOR"] = 58] = "PALFOR";
    ZoneID[ZoneID["PALHIGH"] = 59] = "PALHIGH";
    ZoneID[ZoneID["PALMPOW"] = 60] = "PALMPOW";
    ZoneID[ZoneID["PBLUFF"] = 61] = "PBLUFF";
    ZoneID[ZoneID["PBOX"] = 62] = "PBOX";
    ZoneID[ZoneID["PROCOB"] = 63] = "PROCOB";
    ZoneID[ZoneID["RANCHO"] = 64] = "RANCHO";
    ZoneID[ZoneID["RGLEN"] = 65] = "RGLEN";
    ZoneID[ZoneID["RICHM"] = 66] = "RICHM";
    ZoneID[ZoneID["ROCKF"] = 67] = "ROCKF";
    ZoneID[ZoneID["RTRAK"] = 68] = "RTRAK";
    ZoneID[ZoneID["SanAnd"] = 69] = "SanAnd";
    ZoneID[ZoneID["SANCHIA"] = 70] = "SANCHIA";
    ZoneID[ZoneID["SANDY"] = 71] = "SANDY";
    ZoneID[ZoneID["SKID"] = 72] = "SKID";
    ZoneID[ZoneID["SLAB"] = 73] = "SLAB";
    ZoneID[ZoneID["STAD"] = 74] = "STAD";
    ZoneID[ZoneID["STRAW"] = 75] = "STRAW";
    ZoneID[ZoneID["TATAMO"] = 76] = "TATAMO";
    ZoneID[ZoneID["TERMINA"] = 77] = "TERMINA";
    ZoneID[ZoneID["TEXTI"] = 78] = "TEXTI";
    ZoneID[ZoneID["TONGVAH"] = 79] = "TONGVAH";
    ZoneID[ZoneID["TONGVAV"] = 80] = "TONGVAV";
    ZoneID[ZoneID["VCANA"] = 81] = "VCANA";
    ZoneID[ZoneID["VESP"] = 82] = "VESP";
    ZoneID[ZoneID["VINE"] = 83] = "VINE";
    ZoneID[ZoneID["WINDF"] = 84] = "WINDF";
    ZoneID[ZoneID["WVINE"] = 85] = "WVINE";
    ZoneID[ZoneID["ZANCUDO"] = 86] = "ZANCUDO";
    ZoneID[ZoneID["ZP_ORT"] = 87] = "ZP_ORT";
    ZoneID[ZoneID["ZQ_UAR"] = 88] = "ZQ_UAR";
})(ZoneID || (ZoneID = {}));
