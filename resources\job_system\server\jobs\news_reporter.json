{"id": "news_reporter", "name": "News Reporter", "category": "civilian", "summary": "Gather information, conduct interviews, and report on breaking news stories throughout Los Santos.", "narrative_flavor": "The city has stories to tell, and you're the one to bring them to light. Grab your press pass and get to the scene!", "prerequisites": {"level": 5, "licenses": ["press_pass"], "items_required": ["camera", "microphone"]}, "start": {"how_to_start": "Talk to the news director at the Weazel News building.", "start_locations": [{"label": "Weazel News HQ", "coords": {"x": -598.7408447265625, "y": -933.6737670898438, "z": 23.863, "h": 80.467}, "radius": 20.0}]}, "flow": {"steps": [{"order": 1, "label": "Get Assignment", "description": "Receive a news assignment from the director.", "success_criteria": "Accept the assignment."}, {"order": 2, "label": "Gather Information", "description": "Travel to the scene and gather information by interviewing witnesses or taking pictures.", "success_criteria": "Collect 3 pieces of information."}, {"order": 3, "label": "Report Story", "description": "Return to Weazel News and file your report.", "success_criteria": "Submit the story to the director."}], "average_duration_min": 15}, "mechanics": {"interactions": ["markers", "targets", "progressbars"], "items": {"consumed": [], "produced": ["news_report"]}, "timers": {"per_step_sec": 0, "overall_limit_min": 30}, "random_events": [{"event": "Rival news crew arrives", "chance": 0.2, "effect": "Competition for interviews"}], "police_alerts": {"enabled": true, "conditions": "Reporting on a crime scene"}, "cooldowns": {"per_player_min": 10}, "failure_conditions": ["Fail to meet deadline", "Get arrested"]}, "locations": [{"label": "Crime Scene", "coords": {"x": 221.5, "y": -801.8, "z": 30.7}, "radius": 15.0}, {"label": "Political Rally", "coords": {"x": -138.8, "y": -631.5, "z": 168.8}, "radius": 15.0}, {"label": "Celebrity Sighting", "coords": {"x": -771.9, "y": -333.1, "z": 37.9}, "radius": 15.0}], "npcs": [{"label": "News Director", "model": "ig_beverly", "role": "giver|receiver", "behavior": "idle"}], "props_vehicles": {"vehicles": [{"model": "rumpo2", "required": true, "spawn_rules": "Provided at Weazel News"}], "props": [{"model": "prop_cam_mic_01", "usage": "Interviewing"}]}, "rewards": {"base_pay": 600, "per_task_bonus": 100, "streak_multiplier_max": 1.8, "penalties": [{"reason": "Missed deadline", "amount": 150}]}, "balance": {"difficulty": 3, "risk_vs_reward": "medium", "expected_profit_min": 700, "expected_profit_max": 1200}, "configuration": {"economy_multiplier": 1.0, "police_required": false, "framework": "standalone"}, "events": {"client": ["job:start", "job:step<PERSON><PERSON>plete", "job:finish"], "server": ["job:assign", "job:reward", "job:fail"]}, "dependencies": {"optional": ["ox_lib", "ox_target", "qb-core or es_extended"], "notes": "Can integrate with inventory systems for camera and microphone items."}, "ui_text": {"notifications": ["New assignment available.", "Information gathered.", "Story submitted."], "helptext": ["Go to the location marked on your map.", "Interview the witness.", "Return to Weazel News."], "progress_labels": ["Gathering Information", "Writing Report"]}, "localization_keys": ["job.name", "job.start", "job.complete"], "anti_exploit": ["server-side reward validation", "cooldown checks"], "test_scenarios": ["Player disconnects during assignment", "Player loses required items"], "extensibility": ["Different types of news stories (e.g., sports, weather)", "Live reporting"], "version": "1.0.0"}