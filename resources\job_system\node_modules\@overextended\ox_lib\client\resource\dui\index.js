import { cache } from '../cache';
const duis = {};
let currentId = 0;
export class Dui {
    id = '';
    debug = false;
    url = '';
    duiObject = 0;
    duiHandle = '';
    runtimeTxd = 0;
    txdObject = 0;
    dictName = '';
    txtName = '';
    constructor(data) {
        const time = GetGameTimer();
        const id = `${cache.resource}_${time}_${currentId}`;
        currentId++;
        this.id = id;
        this.debug = data.debug || false;
        this.url = data.url;
        this.dictName = `ox_lib_dui_dict_${id}`;
        this.txtName = `ox_lib_dui_txt_${id}`;
        this.duiObject = CreateDui(data.url, data.width, data.height);
        this.duiHandle = GetDuiHandle(this.duiObject);
        this.runtimeTxd = CreateRuntimeTxd(this.dictName);
        this.txdObject = CreateRuntimeTextureFromDuiHandle(this.runtimeTxd, this.txtName, this.duiHandle);
        duis[id] = this;
        if (this.debug)
            console.log(`Dui ${this.id} created`);
    }
    remove() {
        SetDuiUrl(this.duiObject, 'about:blank');
        DestroyDui(this.duiObject);
        delete duis[this.id];
        if (this.debug)
            console.log(`Dui ${this.id} removed`);
    }
    setUrl(url) {
        this.url = url;
        SetDuiUrl(this.duiObject, url);
        if (this.debug)
            console.log(`Dui ${this.id} url set to ${url}`);
    }
    sendMessage(data) {
        SendDuiMessage(this.duiObject, JSON.stringify(data));
        if (this.debug)
            console.log(`Dui ${this.id} message sent with data :`, data);
    }
}
on('onResourceStop', (resourceName) => {
    if (cache.resource !== resourceName)
        return;
    for (const dui in duis) {
        duis[dui].remove();
    }
});
