interface DuiProperties {
    url: string;
    width: number;
    height: number;
    debug?: boolean;
}
export declare class Dui {
    private id;
    private debug;
    url: string;
    duiObject: number;
    duiHandle: string;
    runtimeTxd: number;
    txdObject: number;
    dictName: string;
    txtName: string;
    constructor(data: DuiProperties);
    remove(): void;
    setUrl(url: string): void;
    sendMessage(data: object): void;
}
export {};
