export var AudioFlag;
(function (AudioFlag) {
    AudioFlag[AudioFlag["ActivateSwitchWheelAudio"] = 0] = "ActivateSwitchWheelAudio";
    AudioFlag[AudioFlag["AllowCutsceneOverScreenFade"] = 1] = "AllowCutsceneOverScreenFade";
    AudioFlag[AudioFlag["AllowForceRadioAfterRetune"] = 2] = "AllowForceRadioAfterRetune";
    AudioFlag[AudioFlag["AllowPainAndAmbientSpeechToPlayDuringCutscene"] = 3] = "AllowPainAndAmbientSpeechToPlayDuringCutscene";
    AudioFlag[AudioFlag["AllowPlayerAIOnMission"] = 4] = "AllowPlayerAIOnMission";
    AudioFlag[AudioFlag["AllowPoliceScannerWhenPlayerHasNoControl"] = 5] = "AllowPoliceScannerWhenPlayerHasNoControl";
    AudioFlag[AudioFlag["AllowRadioDuringSwitch"] = 6] = "AllowRadioDuringSwitch";
    AudioFlag[AudioFlag["AllowRadioOverScreenFade"] = 7] = "AllowRadioOverScreenFade";
    AudioFlag[AudioFlag["AllowScoreAndRadio"] = 8] = "AllowScoreAndRadio";
    AudioFlag[AudioFlag["AllowScriptedSpeechInSlowMo"] = 9] = "AllowScriptedSpeechInSlowMo";
    AudioFlag[AudioFlag["AvoidMissionCompleteDelay"] = 10] = "AvoidMissionCompleteDelay";
    AudioFlag[AudioFlag["DisableAbortConversationForDeathAndInjury"] = 11] = "DisableAbortConversationForDeathAndInjury";
    AudioFlag[AudioFlag["DisableAbortConversationForRagdoll"] = 12] = "DisableAbortConversationForRagdoll";
    AudioFlag[AudioFlag["DisableBarks"] = 13] = "DisableBarks";
    AudioFlag[AudioFlag["DisableFlightMusic"] = 14] = "DisableFlightMusic";
    AudioFlag[AudioFlag["DisableReplayScriptStreamRecording"] = 15] = "DisableReplayScriptStreamRecording";
    AudioFlag[AudioFlag["EnableHeadsetBeep"] = 16] = "EnableHeadsetBeep";
    AudioFlag[AudioFlag["ForceConversationInterrupt"] = 17] = "ForceConversationInterrupt";
    AudioFlag[AudioFlag["ForceSeamlessRadioSwitch"] = 18] = "ForceSeamlessRadioSwitch";
    AudioFlag[AudioFlag["ForceSniperAudio"] = 19] = "ForceSniperAudio";
    AudioFlag[AudioFlag["FrontendRadioDisabled"] = 20] = "FrontendRadioDisabled";
    AudioFlag[AudioFlag["HoldMissionCompleteWhenPrepared"] = 21] = "HoldMissionCompleteWhenPrepared";
    AudioFlag[AudioFlag["IsDirectorModeActive"] = 22] = "IsDirectorModeActive";
    AudioFlag[AudioFlag["IsPlayerOnMissionForSpeech"] = 23] = "IsPlayerOnMissionForSpeech";
    AudioFlag[AudioFlag["ListenerReverbDisabled"] = 24] = "ListenerReverbDisabled";
    AudioFlag[AudioFlag["LoadMPData"] = 25] = "LoadMPData";
    AudioFlag[AudioFlag["MobileRadioInGame"] = 26] = "MobileRadioInGame";
    AudioFlag[AudioFlag["OnlyAllowScriptTriggerPoliceScanner"] = 27] = "OnlyAllowScriptTriggerPoliceScanner";
    AudioFlag[AudioFlag["PlayMenuMusic"] = 28] = "PlayMenuMusic";
    AudioFlag[AudioFlag["PoliceScannerDisabled"] = 29] = "PoliceScannerDisabled";
    AudioFlag[AudioFlag["ScriptedConvListenerMaySpeak"] = 30] = "ScriptedConvListenerMaySpeak";
    AudioFlag[AudioFlag["SpeechDucksScore"] = 31] = "SpeechDucksScore";
    AudioFlag[AudioFlag["SuppressPlayerScubaBreathing"] = 32] = "SuppressPlayerScubaBreathing";
    AudioFlag[AudioFlag["WantedMusicDisabled"] = 33] = "WantedMusicDisabled";
    AudioFlag[AudioFlag["WantedMusicOnMission"] = 34] = "WantedMusicOnMission";
})(AudioFlag || (AudioFlag = {}));
