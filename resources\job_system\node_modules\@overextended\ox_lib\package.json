{"name": "@overextended/ox_lib", "author": "Overextended", "version": "3.30.6", "description": "JS/TS wrapper for ox_lib exports", "main": "./shared/index.js", "types": "./shared/index.d.ts", "sideEffects": ["./shared/resource/locale/index.js"], "scripts": {"compile": "tsc", "prepare": "npm run compile"}, "keywords": ["fivem", "ox_lib", "ox", "overextended"], "repository": {"type": "git", "url": "git+https://github.com/overextended/ox_lib.git"}, "bugs": {"url": "https://github.com/overextended/ox_lib/issues"}, "license": "LGPL-3.0", "dependencies": {"@nativewrappers/client": "^1.7.33", "csstype": "^3.1.3", "fast-printf": "^1.6.9", "typescript": "^5.4.2"}, "devDependencies": {"@citizenfx/client": "latest", "@citizenfx/server": "latest", "@fortawesome/fontawesome-common-types": "6.1.1", "@mantine/core": "^6.0.21", "@types/node": "16.9.1", "@types/react": "^18.2.66", "prettier": "^2.8.8"}}