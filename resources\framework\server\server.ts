import { CreateDocument, DatabaseConnect, ReadDocuments, UpdateDocument } from "database";
import { Character, ConnectingUser, User } from "types";

export const DELAY = (ms: number) => new Promise(res => setTimeout(res, ms));

on("onResourceStart", (resourceName: string) => {
    if (GetCurrentResourceName() != resourceName) {
        return;
    }

    DatabaseConnect();
});

on('playerConnecting', (playerName: string, setKickReason: (reason: string) => void, deferrals: { defer: any; done: any; handover: any; presentCard: any; update: any }, source: string) => {
    deferrals.defer();

    const player = (global as any).source;

    (async () => {
        try {
            deferrals.update(`Hello ${playerName}. Your IP is being checked by the Server...`);

            let ipAddress: string | null = null;
            const playerConnectingObject: ConnectingUser = {
                name: playerName,
                ip: "",
                identifiers: []
            };

            for (let i = 0; i < GetNumPlayerIdentifiers(player); i++) {
                const identifier = GetPlayerIdentifier(player, i);
                // console.log(identifier);
                playerConnectingObject.identifiers.push(identifier);

                if (identifier.startsWith('ip:')) {
                    ipAddress = identifier;
                }
            }

            // pretend to be a wait
            await DELAY(2500);

            if (ipAddress === null) {
                return deferrals.done("Your IP is not valid or could not be found.");
            }

            // console.log(`Player Connecting with Ip Address: ${ipAddress} [${playerName}]`);
            playerConnectingObject.ip = ipAddress;

            const connecting_users = await ReadDocuments('connecting', playerConnectingObject);
            // console.log("Users found from server.ts:", connecting_users);

            if (!connecting_users || connecting_users.length === 0) {
                return deferrals.done("You are not whitelisted.");
            }

            deferrals.update(`Welcome Back ${playerName}`);
            deferrals.update("Checking for your User Data");
            await DELAY(2500);

            const users = await ReadDocuments('users', playerConnectingObject);
            if (users && users.length > 0) {
                deferrals.update("User Data Found");
                await DELAY(2500);
                deferrals.done();
            } else {
                deferrals.update("User Data Not Found.. Creating your User Data.");
                const newUserObject: User = {
                    name: playerConnectingObject.name,
                    ip: playerConnectingObject.ip,
                    identifiers: playerConnectingObject.identifiers,
                    characters: []
                };
                await CreateDocument('users', newUserObject);
                deferrals.update("User Data Created");
                await DELAY(2500);
                deferrals.done();
            }
        } catch (e) {
            console.error("Error during database check for connecting player:", e);
            deferrals.done("An error occurred while verifying your details.");
        }
    })();
});

on('playerJoining', () => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} is joining.`);
    emitNet('framework:user_ready', src);
});

onNet('framework:server_gather', async () => {
     const src = (global as any).source;
     const users = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
     const characters = users[0].characters;
     TriggerClientEvent('framework:showCharacterSelection', src, characters);
});

// Let's assume your character selection UI, once finished, triggers this server event.
onNet('framework:characterSelected', async (characterData: any) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} selected character:`, characterData);
    // Here you would load the character's data, set their skin, inventory, etc.
    // Once everything is loaded, tell the client to spawn.
    TriggerClientEvent(src, 'framework:spawnPlayer');
});

onNet('framework:server_deleteCharacter', async (characterData: any) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} is deleting character:`, characterData.firstName, characterData.lastName);

    // A real-world scenario would use a unique character ID for safety.
    // Here, we'll match on a combination of fields.
    const users = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
    if (users && users.length > 0) {
        await UpdateDocument('users', users[0], {
            $pull: {
                characters: {
                    firstName: characterData.firstName,
                    lastName: characterData.lastName,
                    dob: characterData.dob
                }
            }
        });

        const updatedCharacters = (await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) }))[0].characters;
        TriggerClientEvent('framework:showCharacterSelection', src, updatedCharacters);
    }
});

onNet('framework:createCharacter', async ( characterData: any ) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} wants to create a new character.`);
    console.log(characterData);
    let ipAddress: string | null = null;
    for (let i = 0; i < GetNumPlayerIdentifiers(src); i++) {
                const identifier = GetPlayerIdentifier(src, i);
                if (identifier.startsWith('ip:')) {
                    ipAddress = identifier;
                }
            }
    const newCharacter: Character = {
        firstName: characterData.firstName,
        lastName: characterData.lastName,
        gender: characterData.gender,
        dob: characterData.dob,
        ip: ipAddress,
        identifiers: getPlayerIdentifiers(src),
        health: 100,
        armor: 0,
        hunger: 100,
        thirst: 100,
        currentJob: {
            name: null,
            progress: null,
            startTime: null,
            endTime: null,
            status: null,
            data: undefined
        },
        currentGangGroup: [],
        banking: {
            accounts: []
        },
        inventory: {
            items: []
        },
        vehicles: [],
        lastPlayed: Date.now(),
        position: {
            x: 0,
            y: 0,
            z: 0,
            heading: 0
        },
        appearance: {
            model: characterData.gender === 'male' ? 'mp_m_freemode_01' : 'mp_f_freemode_01',
            components: [null],
            props: [null],
            tattoos: [null]
        }
    }

    const users = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
    await UpdateDocument('users', users[0] , { $push: { characters: newCharacter } });

    // Re-fetch the user to get the updated character list
    const updatedUsers = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
    const updatedCharacters = updatedUsers[0].characters;

    // Send the new list back to the client to display
    TriggerClientEvent('framework:showCharacterSelection', src, updatedCharacters);
});