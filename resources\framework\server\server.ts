import { CreateDocument, DatabaseConnect, ReadDocuments, UpdateDocument } from "database";
import * as crypto from 'crypto';
import { Character, ConnectingUser, User } from "types";

export const DELAY = (ms: number) => new Promise(res => setTimeout(res, ms));

on("onResourceStart", (resourceName: string) => {
    if (GetCurrentResourceName() != resourceName) {
        return;
    }

    DatabaseConnect();
});

on('playerConnecting', (playerName: string, setKickReason: (reason: string) => void, deferrals: { defer: any; done: any; handover: any; presentCard: any; update: any }, source: string) => {
    deferrals.defer();

    const player = (global as any).source;

    (async () => {
        try {
            deferrals.update(`Hello ${playerName}. Your IP is being checked by the Server...`);

            let ipAddress: string | null = null;
            const playerConnectingObject: ConnectingUser = {
                name: playerN<PERSON>,
                ip: "",
                identifiers: []
            };

            for (let i = 0; i < GetNumPlayerIdentifiers(player); i++) {
                const identifier = GetPlayerIdentifier(player, i);
                // console.log(identifier);
                playerConnectingObject.identifiers.push(identifier);

                if (identifier.startsWith('ip:')) {
                    ipAddress = identifier;
                }
            }

            // pretend to be a wait
            await DELAY(2500);

            if (ipAddress === null) {
                return deferrals.done("Your IP is not valid or could not be found.");
            }

            // console.log(`Player Connecting with Ip Address: ${ipAddress} [${playerName}]`);
            playerConnectingObject.ip = ipAddress;

            const connecting_users = await ReadDocuments('connecting', playerConnectingObject);
            // console.log("Users found from server.ts:", connecting_users);

            if (!connecting_users || connecting_users.length === 0) {
                return deferrals.done("You are not whitelisted.");
            }

            deferrals.update(`Welcome Back ${playerName}`);
            deferrals.update("Checking for your User Data");
            await DELAY(2500);

            const users = await ReadDocuments('users', playerConnectingObject);
            if (users && users.length > 0) {
                deferrals.update("User Data Found");
                await DELAY(2500);
                deferrals.done();
            } else {
                deferrals.update("User Data Not Found.. Creating your User Data.");
                const newUserObject: User = {
                    name: playerConnectingObject.name,
                    ip: playerConnectingObject.ip,
                    identifiers: playerConnectingObject.identifiers,
                    characters: []
                };
                await CreateDocument('users', newUserObject);
                deferrals.update("User Data Created");
                await DELAY(2500);
                deferrals.done();
            }
        } catch (e) {
            console.error("Error during database check for connecting player:", e);
            deferrals.done("An error occurred while verifying your details.");
        }
    })();
});

on('playerJoining', () => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} is joining.`);
    emitNet('framework:user_ready', src);
});

onNet('framework:server_gather', async () => {
     const src = (global as any).source;
     const users = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
     const characters = users[0].characters;
     TriggerClientEvent('framework:showCharacterSelection', src, characters);
});

// Let's assume your character selection UI, once finished, triggers this server event.
onNet('framework:characterSelected', async (characterData: any) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} selected character:`, characterData);
    // Here you would load the character's data, set their skin, inventory, etc.
    // Once everything is loaded, tell the client to spawn.
    TriggerClientEvent(src, 'framework:spawnPlayer');
});

onNet('framework:server_deleteCharacter', async (characterData: any) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} is deleting character:`, characterData.firstName, characterData.lastName);

    const users = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
    if (users && users.length > 0) {
        // Determine the query to use for pulling the character from the array.
        // This handles both new characters with UUIDs and old characters without.
        const pullQuery = characterData.id
            ? { id: characterData.id } // Use the unique ID if it exists
            : { // Fallback to old properties if no ID is present
                firstName: characterData.firstName,
                lastName: characterData.lastName,
                dob: characterData.dob
              };

        await UpdateDocument('users', users[0], {
            $pull: {
                characters: pullQuery
            }
        });

        const updatedCharacters = (await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) }))[0].characters;
        TriggerClientEvent('framework:showCharacterSelection', src, updatedCharacters);
    }
});

onNet('framework:createCharacter', async ( characterData: any ) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} wants to create a new character.`);
    let ipAddress: string | null = null;
    for (let i = 0; i < GetNumPlayerIdentifiers(src); i++) {
                const identifier = GetPlayerIdentifier(src, i);
                if (identifier.startsWith('ip:')) {
                    ipAddress = identifier;
                }
            }
    const newCharacter: Character = {
        id: crypto.randomUUID(),
        firstName: characterData.firstName,
        lastName: characterData.lastName,
        gender: characterData.gender,
        dob: characterData.dob,
        ip: ipAddress,
        identifiers: getPlayerIdentifiers(src),
        health: 100,
        armor: 0,
        hunger: 100,
        thirst: 100,
        currentJob: {
            name: null,
            progress: null,
            startTime: null,
            endTime: null,
            status: null,
            data: undefined
        },
        currentGangGroup: [],
        banking: {
            accounts: []
        },
        inventory: {
            items: []
        },
        vehicles: [],
        lastPlayed: Date.now(),
        position: {
            x: -1045.9,
            y: -2751.55,
            z: 8.38,
            heading: 0
        },
        appearance: {
            model: characterData.gender === 'male' ? 'mp_m_freemode_01' : 'mp_f_freemode_01' ,
            components: [],
            props: [],
            tattoos: [],
            eyeColor: 0,
            faceFeatures: {
                cheeksBoneHigh: 0,
                cheeksBoneWidth: 0,
                cheeksWidth: 0,
                chinBoneLenght: 0,
                chinBoneLowering: 0,
                chinBoneSize: 0,
                chinHole: 0,
                eyeBrownForward: 0,
                eyeBrownHigh: 0,
                eyesOpening: 0,
                jawBoneBackSize: 0,
                jawBoneWidth: 0,
                lipsThickness: 0,
                neckThickness: 0,
                noseBoneHigh: 0,
                noseBoneTwist: 0,
                nosePeakHigh: 0,
                nosePeakLowering: 0,
                nosePeakSize: 0,
                noseWidth: 0
            },
            hair: {
                color: 0,
                highlight: 0,
                style: 0
            },
            headBlend: {
                shapeFirst: 0,
                shapeSecond: 0,
                shapeMix: 0,
                skinFirst: 0,
                skinSecond: 0,
                skinMix: 0
            },
            headOverlays: {
                ageing: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                beard: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                blemishes: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                blush: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                bodyBlemishes: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                chestHair: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                complexion: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                eyebrows: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                lipstick: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                makeUp: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                moleAndFreckles: {
                    color: 0,
                    opacity: 0,
                    style: 0
                },
                sunDamage: {
                    color: 0,
                    opacity: 0,
                    style: 0
                }
            }
        },
        phoneDetails: {
            info: []
        }
    }

    const users = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
    await UpdateDocument('users', users[0] , { $push: { characters: newCharacter } });

    // Re-fetch the user to get the updated character list
    const updatedUsers = await ReadDocuments('users', { identifiers: getPlayerIdentifiers(src) });
    const updatedCharacters = updatedUsers[0].characters;

    // Send the new list back to the client to display
    TriggerClientEvent('framework:showCharacterSelection', src, updatedCharacters);
});

onNet('framework:saveAppearanceData', async (characterId: string, appearanceData: any) => {
    const src = (global as any).source;
    console.log(`Player ${GetPlayerName(src as string)} wants to save their appearance data.`);

    // Use a positional operator ($) to update the appearance of a specific character in the array.
    // This is safer than replacing the entire array and prevents race conditions.
    await UpdateDocument('users', { identifiers: getPlayerIdentifiers(src), "characters.id": characterId }, {
        $set: { "characters.$.appearance": JSON.parse(appearanceData) }
    });
});