import { Vector3 } from '@nativewrappers/client';
interface LibPoint<T = unknown> {
    coords: number[];
    distance: number;
    onEnter?: () => void;
    onExit?: () => void;
    nearby?: () => void;
    args?: T;
}
export declare class Point<T = unknown> {
    id: number;
    coords: Vector3;
    distance: number;
    onEnter?: () => void;
    onExit?: () => void;
    nearby?: () => void;
    args?: T;
    inside: boolean;
    currentDistance?: number;
    isClosest: boolean;
    constructor(point: LibPoint<T>);
    remove: () => void;
}
export {};
