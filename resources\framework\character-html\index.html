<!DOCTYPE html>
<html>
<head>
    <title>Character Selection</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Character Selection</h1>
        <div class="character-list">
            <!-- Character items will be dynamically loaded here -->

        </div>
    </div>

    <div class="creation-container">
        <h1>Create New Character</h1>
        <form id="creation-form">
            <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            <div class="form-group">
                <label for="gender">Gender</label>
                <select id="gender" name="gender" required>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                </select>
            </div>
            <div class="form-group">
                <label for="dob">Date of Birth</label>
                <input type="date" id="dob" name="dob" required>
            </div>
            <div class="form-actions">
                <button type="button" id="back-button">Back</button>
                <button type="submit" id="create-character-button">Create Character</button>
            </div>
        </form>
    </div>

    <div id="delete-confirm-dialog" class="dialog-overlay">
        <div class="dialog-box">
            <h2>Confirm Deletion</h2>
            <p>Are you sure you want to delete this character? This action cannot be undone.</p>
            <div class="dialog-actions">
                <button id="cancel-delete-button">Cancel</button>
                <button id="confirm-delete-button">Confirm</button>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>
