/**
 * List of Zones. Useful for using world zone related natives.
 */
export declare enum ZoneID {
    AIRP = 0,
    ALAMO = 1,
    ALTA = 2,
    ARMYB = 3,
    BANHAMC = 4,
    BANNING = 5,
    BEACH = 6,
    BHAMCA = 7,
    BRADP = 8,
    BRADT = 9,
    BURTON = 10,
    CALAFB = 11,
    CANNY = 12,
    CCREAK = 13,
    CHAMH = 14,
    CHIL = 15,
    CHU = 16,
    CMSW = 17,
    CYPRE = 18,
    DAVIS = 19,
    DELBE = 20,
    DELPE = 21,
    DELSOL = 22,
    DESRT = 23,
    DOWNT = 24,
    DTVINE = 25,
    EAST_V = 26,
    EBURO = 27,
    ELGORL = 28,
    ELYSIAN = 29,
    GALFISH = 30,
    golf = 31,
    GRAPES = 32,
    GREATC = 33,
    HARMO = 34,
    HAWICK = 35,
    HORS = 36,
    HUMLAB = 37,
    JAIL = 38,
    KOREAT = 39,
    LACT = 40,
    LAGO = 41,
    LDAM = 42,
    LEGSQU = 43,
    LMESA = 44,
    LOSPUER = 45,
    MIRR = 46,
    MORN = 47,
    MOVIE = 48,
    MTCHIL = 49,
    <PERSON>T<PERSON><PERSON><PERSON><PERSON> = 50,
    MT<PERSON><PERSON><PERSON> = 51,
    <PERSON>URRI = 52,
    <PERSON>HU = 53,
    NOOSE = 54,
    OCEANA = 55,
    PALCOV = 56,
    PALETO = 57,
    PALFOR = 58,
    PALHIGH = 59,
    PALMPOW = 60,
    PBLUFF = 61,
    PBOX = 62,
    PROCOB = 63,
    RANCHO = 64,
    RGLEN = 65,
    RICHM = 66,
    ROCKF = 67,
    RTRAK = 68,
    SanAnd = 69,
    SANCHIA = 70,
    SANDY = 71,
    SKID = 72,
    SLAB = 73,
    STAD = 74,
    STRAW = 75,
    TATAMO = 76,
    TERMINA = 77,
    TEXTI = 78,
    TONGVAH = 79,
    TONGVAV = 80,
    VCANA = 81,
    VESP = 82,
    VINE = 83,
    WINDF = 84,
    WVINE = 85,
    ZANCUDO = 86,
    ZP_ORT = 87,
    ZQ_UAR = 88
}
