const cacheEvents = {};
export const cache = new Proxy({
    resource: GetCurrentResourceName(),
    game: GetGameName(),
}, {
    get(target, key) {
        const result = key ? target[key] : target;
        if (result !== undefined)
            return result;
        cacheEvents[key] = [];
        AddEventHandler(`ox_lib:cache:${key}`, (value) => {
            const oldValue = target[key];
            const events = cacheEvents[key];
            events.forEach((cb) => cb(value, oldValue));
            target[key] = value;
        });
        target[key] = exports.ox_lib.cache(key) || false;
        return target[key];
    },
});
export const onCache = (key, cb) => {
    if (!cacheEvents[key])
        cache[key];
    cacheEvents[key].push(cb);
};
