/**
 * The ConnectingUser interface represents a user who is connecting to the server, its only used on the inital connection.
 * --
 * The User interface is our main object, it contains all the data we need to identify a user, this will be used to reference the users characters.
 * --
 * The Character interface is used to represent a users character, this is what we will be using to store the users data.
 */

export interface ConnectingUser {
    name: string;
    ip: string;
    identifiers: string[];
}

export interface User {
    name: string;
    ip: string;
    identifiers: string[];
    characters: Character[];
}

export interface AppearanceData {
    components: {
        component_id: number;
        drawable: number;
        texture: number;
    }[];
    eyeColor: number;
    faceFeatures: {
        cheeksBoneHigh: number;
        cheeksBoneWidth: number;
        cheeksWidth: number;
        chinBoneLenght: number;
        chinBoneLowering: number;
        chinBoneSize: number;
        chinHole: number            
        eyeBrownForward: number;
        eyeBrownHigh: number;
        eyesOpening: number;
        jawBoneBackSize: number;
        jawBoneWidth: number;
        lipsThickness: number;
        neckThickness: number;
        noseBoneHigh: number;
        noseBoneTwist: number;
        nosePeakHigh: number;
        nosePeakLowering: number;
        nosePeakSize: number;
        noseWidth: number;
    };
    hair: {
        color: number;
        highlight: number;
        style: number;
    };
    headBlend: {
        shapeFirst: number;
        shapeSecond: number;
        shapeMix: number;
        skinFirst: number;
        skinSecond: number;
        skinMix: number;
    };
    headOverlays: {
        ageing: {
            color: number;
            opacity: number;
            style: number;
        };
        beard: {
            color: number;
            opacity: number;
            style: number;
        };
        blemishes: {
            color: number;
            opacity: number;
            style: number;
        };
        blush: {
            color: number;
            opacity: number;
            style: number;
        };
        bodyBlemishes: {
            color: number;
            opacity: number;
            style: number;
        };
        chestHair: {
            color: number;
            opacity: number;
            style: number;
        };
        complexion: {
            color: number;
            opacity: number;
            style: number;
        };
        eyebrows: {
            color: number;
            opacity: number;
            style: number;
        };
        lipstick: {
            color: number;
            opacity: number;
            style: number;
        };
        makeUp: {
            color: number;
            opacity: number;
            style: number;
        };
        moleAndFreckles: {
            color: number;
            opacity: number;
            style: number;
        };
        sunDamage: {
            color: number;
            opacity: number;
            style: number;
        };
    };
    model: string;
    props: {
        drawable: number;
        prop_id: number;
        texture: number;
    }[];
    tattoos: {
        
    };  
}

export interface Character {
    id: string;
    firstName: string;
    lastName: string;
    gender: string;
    dob: string;
    ip: string;
    phoneDetails: {
        info: [{number: string; active: boolean}] | [];
    }
    identifiers: string[];
    health: number;
    armor: number;
    hunger: number;
    thirst: number;
    position: {
        x: number;
        y: number;
        z: number;
        heading: number;
    };
    appearance: AppearanceData | null;
    currentJob: {
        name: string;
        progress: number;
        startTime: number;
        endTime: number;
        status: string;
        data: any;
    };
    currentGangGroup: number[];    
    banking: {
        accounts: {
            name: string;
            balance: number;
        }[];
    };
    inventory: {
        items: {
            name: string;
            amount: number;
        }[];
    };
    vehicles: {
        model: string;
        plate: string;
        garage: string;
        spawned: boolean;
        stored_location: string;
        modifications: [any];
        fuel: number;
        health: number;
        bodyHealth: number;
    }[]; 
    lastPlayed: number;

}