/**
 * The ConnectingUser interface represents a user who is connecting to the server, its only used on the inital connection.
 * --
 * The User interface is our main object, it contains all the data we need to identify a user, this will be used to reference the users characters.
 * --
 * The Character interface is used to represent a users character, this is what we will be using to store the users data.
 */

export interface ConnectingUser {
    name: string;
    ip: string;
    identifiers: string[];
}

export interface User {
    name: string;
    ip: string;
    identifiers: string[];
    characters: Character[];
}

export interface Character {
    firstName: string;
    lastName: string;
    gender: string;
    dob: string;
    ip: string;
    identifiers: string[];
    health: number;
    armor: number;
    hunger: number;
    thirst: number;
    position: {
        x: number;
        y: number;
        z: number;
        heading: number;
    };
    appearance: {
        model: string;
        components: [any];
        props: [any];
        tattoos: [any];
    };
    currentJob: {
        name: string;
        progress: number;
        startTime: number;
        endTime: number;
        status: string;
        data: any;
    };
    currentGangGroup: number[];    
    banking: {
        accounts: {
            name: string;
            balance: number;
        }[];
    };
    inventory: {
        items: {
            name: string;
            amount: number;
        }[];
    };
    vehicles: {
        model: string;
        plate: string;
        garage: string;
        spawned: boolean;
        stored_location: string;
        modifications: [any];
        fuel: number;
        health: number;
        bodyHealth: number;
    }[]; 
    lastPlayed: number;

}