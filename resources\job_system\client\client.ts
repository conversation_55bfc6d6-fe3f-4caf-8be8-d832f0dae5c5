import lib, { requestModel } from '@overextended/ox_lib/client';
import { Job } from 'types';


onNet('ollama_ped:interact_with_model_start', () => {
    emit('ollama_ped:job_finding_notification');
});

onNet('ollama_ped:job_finding_notification', () => {
    let ped = PlayerPedId()
    PlayPedRingtone("Dial_and_Remote_Ring", ped, true)
    FreezeEntityPosition(ped, true);
    emit("ollama_ped:phone_call_animation", ped)
    lib.notify({
        title: 'Labor Office',
        description: 'Were attempting to find your lazy ass a job, please hold...',
        duration: 5000,
        type: 'success',
    });
    emitNet('ollama_ped:gather_labor_job');
});

onNet('ollama_ped:job_reply', async (msg: any) => {
    let ped = PlayerPedId()
    StopPedRingtone(ped)
    let job: Job = JSON.parse(msg);
    const alert = await lib.alertDialog({
        header: job.name,
        content: job.summary,
        centered: true,
        cancel: true,
    });
    emit("ollama_ped:phone_call_animation", ped)
    if (alert === 'confirm') {

        SetNewWaypoint(job.start.start_locations[0].coords.x, job.start.start_locations[0].coords.y)
        PlaySoundFrontend(-1, "Phone_Text_Arrive", "DLC_H4_MM_Sounds", true)

        lib.notify({
            title: job.start.start_locations[0].label,
            description: job.start.how_to_start,
            duration: 7500,
            type: 'success',
        })

        FreezeEntityPosition(ped, false);

        await requestModel(job.npcs[0].model);

        const [foundGround, groundZ] = GetGroundZFor_3dCoord(job.start.start_locations[0].coords.x, job.start.start_locations[0].coords.y, job.start.start_locations[0].coords.z, false);

        if (!foundGround) {
            console.error(`[ollama_ped:client] Could not find ground for job ${job.name} at ${JSON.stringify(job.start.start_locations[0].coords)}`);
            return;
        } else {
            job.start.start_locations[0].coords.z = groundZ;
        }

        let createdPed = CreatePed(0, job.npcs[0].model, job.start.start_locations[0].coords.x, job.start.start_locations[0].coords.y, job.start.start_locations[0].coords.z, job.start.start_locations[0].coords.h, true, true);
        
        SetModelAsNoLongerNeeded(createdPed);
        FreezeEntityPosition(createdPed, true);
        emit("ollama_ped:job_ped_zone", createdPed, job)

    } else {
        lib.notify({
            title: 'Labor Office',
            description: 'What a bum, dont bother calling back for awhile...',
            type: 'error',
        });
        FreezeEntityPosition(ped, false);
    }


});
