export var CargobobHook;
(function (CargobobHook) {
    CargobobHook[CargobobHook["Hook"] = 0] = "Hook";
    CargobobHook[CargobobHook["Magnet"] = 1] = "Magnet";
})(CargobobHook || (CargobobHook = {}));
export var LicensePlateStyle;
(function (LicensePlateStyle) {
    LicensePlateStyle[LicensePlateStyle["BlueOnWhite1"] = 3] = "BlueOnWhite1";
    LicensePlateStyle[LicensePlateStyle["BlueOnWhite2"] = 0] = "BlueOnWhite2";
    LicensePlateStyle[LicensePlateStyle["BlueOnWhite3"] = 4] = "BlueOnWhite3";
    LicensePlateStyle[LicensePlateStyle["YellowOnBlack"] = 1] = "YellowOnBlack";
    LicensePlateStyle[LicensePlateStyle["YellowOnBlue"] = 2] = "YellowOnBlue";
    LicensePlateStyle[LicensePlateStyle["NorthYankton"] = 5] = "NorthYankton";
})(LicensePlateStyle || (LicensePlateStyle = {}));
export var LicensePlateType;
(function (LicensePlateType) {
    LicensePlateType[LicensePlateType["FrontAndRearPlates"] = 0] = "FrontAndRearPlates";
    LicensePlateType[LicensePlateType["FrontPlate"] = 1] = "FrontPlate";
    LicensePlateType[LicensePlateType["RearPlate"] = 2] = "RearPlate";
    LicensePlateType[LicensePlateType["None"] = 3] = "None";
})(LicensePlateType || (LicensePlateType = {}));
export var VehicleClass;
(function (VehicleClass) {
    VehicleClass[VehicleClass["Compacts"] = 0] = "Compacts";
    VehicleClass[VehicleClass["Sedans"] = 1] = "Sedans";
    VehicleClass[VehicleClass["SUVs"] = 2] = "SUVs";
    VehicleClass[VehicleClass["Coupes"] = 3] = "Coupes";
    VehicleClass[VehicleClass["Muscle"] = 4] = "Muscle";
    VehicleClass[VehicleClass["SportsClassics"] = 5] = "SportsClassics";
    VehicleClass[VehicleClass["Sports"] = 6] = "Sports";
    VehicleClass[VehicleClass["Super"] = 7] = "Super";
    VehicleClass[VehicleClass["Motorcycles"] = 8] = "Motorcycles";
    VehicleClass[VehicleClass["OffRoad"] = 9] = "OffRoad";
    VehicleClass[VehicleClass["Industrial"] = 10] = "Industrial";
    VehicleClass[VehicleClass["Utility"] = 11] = "Utility";
    VehicleClass[VehicleClass["Vans"] = 12] = "Vans";
    VehicleClass[VehicleClass["Cycles"] = 13] = "Cycles";
    VehicleClass[VehicleClass["Boats"] = 14] = "Boats";
    VehicleClass[VehicleClass["Helicopters"] = 15] = "Helicopters";
    VehicleClass[VehicleClass["Planes"] = 16] = "Planes";
    VehicleClass[VehicleClass["Service"] = 17] = "Service";
    VehicleClass[VehicleClass["Emergency"] = 18] = "Emergency";
    VehicleClass[VehicleClass["Military"] = 19] = "Military";
    VehicleClass[VehicleClass["Commercial"] = 20] = "Commercial";
    VehicleClass[VehicleClass["Trains"] = 21] = "Trains";
})(VehicleClass || (VehicleClass = {}));
export var VehicleColor;
(function (VehicleColor) {
    VehicleColor[VehicleColor["MetallicBlack"] = 0] = "MetallicBlack";
    VehicleColor[VehicleColor["MetallicGraphiteBlack"] = 1] = "MetallicGraphiteBlack";
    VehicleColor[VehicleColor["MetallicBlackSteel"] = 2] = "MetallicBlackSteel";
    VehicleColor[VehicleColor["MetallicDarkSilver"] = 3] = "MetallicDarkSilver";
    VehicleColor[VehicleColor["MetallicSilver"] = 4] = "MetallicSilver";
    VehicleColor[VehicleColor["MetallicBlueSilver"] = 5] = "MetallicBlueSilver";
    VehicleColor[VehicleColor["MetallicSteelGray"] = 6] = "MetallicSteelGray";
    VehicleColor[VehicleColor["MetallicShadowSilver"] = 7] = "MetallicShadowSilver";
    VehicleColor[VehicleColor["MetallicStoneSilver"] = 8] = "MetallicStoneSilver";
    VehicleColor[VehicleColor["MetallicMidnightSilver"] = 9] = "MetallicMidnightSilver";
    VehicleColor[VehicleColor["MetallicGunMetal"] = 10] = "MetallicGunMetal";
    VehicleColor[VehicleColor["MetallicAnthraciteGray"] = 11] = "MetallicAnthraciteGray";
    VehicleColor[VehicleColor["MatteBlack"] = 12] = "MatteBlack";
    VehicleColor[VehicleColor["MatteGray"] = 13] = "MatteGray";
    VehicleColor[VehicleColor["MatteLightGray"] = 14] = "MatteLightGray";
    VehicleColor[VehicleColor["UtilBlack"] = 15] = "UtilBlack";
    VehicleColor[VehicleColor["UtilBlackPoly"] = 16] = "UtilBlackPoly";
    VehicleColor[VehicleColor["UtilDarksilver"] = 17] = "UtilDarksilver";
    VehicleColor[VehicleColor["UtilSilver"] = 18] = "UtilSilver";
    VehicleColor[VehicleColor["UtilGunMetal"] = 19] = "UtilGunMetal";
    VehicleColor[VehicleColor["UtilShadowSilver"] = 20] = "UtilShadowSilver";
    VehicleColor[VehicleColor["WornBlack"] = 21] = "WornBlack";
    VehicleColor[VehicleColor["WornGraphite"] = 22] = "WornGraphite";
    VehicleColor[VehicleColor["WornSilverGray"] = 23] = "WornSilverGray";
    VehicleColor[VehicleColor["WornSilver"] = 24] = "WornSilver";
    VehicleColor[VehicleColor["WornBlueSilver"] = 25] = "WornBlueSilver";
    VehicleColor[VehicleColor["WornShadowSilver"] = 26] = "WornShadowSilver";
    VehicleColor[VehicleColor["MetallicRed"] = 27] = "MetallicRed";
    VehicleColor[VehicleColor["MetallicTorinoRed"] = 28] = "MetallicTorinoRed";
    VehicleColor[VehicleColor["MetallicFormulaRed"] = 29] = "MetallicFormulaRed";
    VehicleColor[VehicleColor["MetallicBlazeRed"] = 30] = "MetallicBlazeRed";
    VehicleColor[VehicleColor["MetallicGracefulRed"] = 31] = "MetallicGracefulRed";
    VehicleColor[VehicleColor["MetallicGarnetRed"] = 32] = "MetallicGarnetRed";
    VehicleColor[VehicleColor["MetallicDesertRed"] = 33] = "MetallicDesertRed";
    VehicleColor[VehicleColor["MetallicCabernetRed"] = 34] = "MetallicCabernetRed";
    VehicleColor[VehicleColor["MetallicCandyRed"] = 35] = "MetallicCandyRed";
    VehicleColor[VehicleColor["MetallicSunriseOrange"] = 36] = "MetallicSunriseOrange";
    VehicleColor[VehicleColor["MetallicClassicGold"] = 37] = "MetallicClassicGold";
    VehicleColor[VehicleColor["MetallicOrange"] = 38] = "MetallicOrange";
    VehicleColor[VehicleColor["MatteRed"] = 39] = "MatteRed";
    VehicleColor[VehicleColor["MatteDarkRed"] = 40] = "MatteDarkRed";
    VehicleColor[VehicleColor["MatteOrange"] = 41] = "MatteOrange";
    VehicleColor[VehicleColor["MatteYellow"] = 42] = "MatteYellow";
    VehicleColor[VehicleColor["UtilRed"] = 43] = "UtilRed";
    VehicleColor[VehicleColor["UtilBrightRed"] = 44] = "UtilBrightRed";
    VehicleColor[VehicleColor["UtilGarnetRed"] = 45] = "UtilGarnetRed";
    VehicleColor[VehicleColor["WornRed"] = 46] = "WornRed";
    VehicleColor[VehicleColor["WornGoldenRed"] = 47] = "WornGoldenRed";
    VehicleColor[VehicleColor["WornDarkRed"] = 48] = "WornDarkRed";
    VehicleColor[VehicleColor["MetallicDarkGreen"] = 49] = "MetallicDarkGreen";
    VehicleColor[VehicleColor["MetallicRacingGreen"] = 50] = "MetallicRacingGreen";
    VehicleColor[VehicleColor["MetallicSeaGreen"] = 51] = "MetallicSeaGreen";
    VehicleColor[VehicleColor["MetallicOliveGreen"] = 52] = "MetallicOliveGreen";
    VehicleColor[VehicleColor["MetallicGreen"] = 53] = "MetallicGreen";
    VehicleColor[VehicleColor["MetallicGasolineBlueGreen"] = 54] = "MetallicGasolineBlueGreen";
    VehicleColor[VehicleColor["MatteLimeGreen"] = 55] = "MatteLimeGreen";
    VehicleColor[VehicleColor["UtilDarkGreen"] = 56] = "UtilDarkGreen";
    VehicleColor[VehicleColor["UtilGreen"] = 57] = "UtilGreen";
    VehicleColor[VehicleColor["WornDarkGreen"] = 58] = "WornDarkGreen";
    VehicleColor[VehicleColor["WornGreen"] = 59] = "WornGreen";
    VehicleColor[VehicleColor["WornSeaWash"] = 60] = "WornSeaWash";
    VehicleColor[VehicleColor["MetallicMidnightBlue"] = 61] = "MetallicMidnightBlue";
    VehicleColor[VehicleColor["MetallicDarkBlue"] = 62] = "MetallicDarkBlue";
    VehicleColor[VehicleColor["MetallicSaxonyBlue"] = 63] = "MetallicSaxonyBlue";
    VehicleColor[VehicleColor["MetallicBlue"] = 64] = "MetallicBlue";
    VehicleColor[VehicleColor["MetallicMarinerBlue"] = 65] = "MetallicMarinerBlue";
    VehicleColor[VehicleColor["MetallicHarborBlue"] = 66] = "MetallicHarborBlue";
    VehicleColor[VehicleColor["MetallicDiamondBlue"] = 67] = "MetallicDiamondBlue";
    VehicleColor[VehicleColor["MetallicSurfBlue"] = 68] = "MetallicSurfBlue";
    VehicleColor[VehicleColor["MetallicNauticalBlue"] = 69] = "MetallicNauticalBlue";
    VehicleColor[VehicleColor["MetallicBrightBlue"] = 70] = "MetallicBrightBlue";
    VehicleColor[VehicleColor["MetallicPurpleBlue"] = 71] = "MetallicPurpleBlue";
    VehicleColor[VehicleColor["MetallicSpinnakerBlue"] = 72] = "MetallicSpinnakerBlue";
    VehicleColor[VehicleColor["MetallicUltraBlue"] = 73] = "MetallicUltraBlue";
    VehicleColor[VehicleColor["UtilDarkBlue"] = 75] = "UtilDarkBlue";
    VehicleColor[VehicleColor["UtilMidnightBlue"] = 76] = "UtilMidnightBlue";
    VehicleColor[VehicleColor["UtilBlue"] = 77] = "UtilBlue";
    VehicleColor[VehicleColor["UtilSeaFoamBlue"] = 78] = "UtilSeaFoamBlue";
    VehicleColor[VehicleColor["UtilLightningBlue"] = 79] = "UtilLightningBlue";
    VehicleColor[VehicleColor["UtilMauiBluePoly"] = 80] = "UtilMauiBluePoly";
    VehicleColor[VehicleColor["UtilBrightBlue"] = 81] = "UtilBrightBlue";
    VehicleColor[VehicleColor["MatteDarkBlue"] = 82] = "MatteDarkBlue";
    VehicleColor[VehicleColor["MatteBlue"] = 83] = "MatteBlue";
    VehicleColor[VehicleColor["MatteMidnightBlue"] = 84] = "MatteMidnightBlue";
    VehicleColor[VehicleColor["WornDarkBlue"] = 85] = "WornDarkBlue";
    VehicleColor[VehicleColor["WornBlue"] = 86] = "WornBlue";
    VehicleColor[VehicleColor["WornLightBlue"] = 87] = "WornLightBlue";
    VehicleColor[VehicleColor["MetallicTaxiYellow"] = 88] = "MetallicTaxiYellow";
    VehicleColor[VehicleColor["MetallicRaceYellow"] = 89] = "MetallicRaceYellow";
    VehicleColor[VehicleColor["MetallicBronze"] = 90] = "MetallicBronze";
    VehicleColor[VehicleColor["MetallicYellowBird"] = 91] = "MetallicYellowBird";
    VehicleColor[VehicleColor["MetallicLime"] = 92] = "MetallicLime";
    VehicleColor[VehicleColor["MetallicChampagne"] = 93] = "MetallicChampagne";
    VehicleColor[VehicleColor["MetallicPuebloBeige"] = 94] = "MetallicPuebloBeige";
    VehicleColor[VehicleColor["MetallicDarkIvory"] = 95] = "MetallicDarkIvory";
    VehicleColor[VehicleColor["MetallicChocoBrown"] = 96] = "MetallicChocoBrown";
    VehicleColor[VehicleColor["MetallicGoldenBrown"] = 97] = "MetallicGoldenBrown";
    VehicleColor[VehicleColor["MetallicLightBrown"] = 98] = "MetallicLightBrown";
    VehicleColor[VehicleColor["MetallicStrawBeige"] = 99] = "MetallicStrawBeige";
    VehicleColor[VehicleColor["MetallicMossBrown"] = 100] = "MetallicMossBrown";
    VehicleColor[VehicleColor["MetallicBistonBrown"] = 101] = "MetallicBistonBrown";
    VehicleColor[VehicleColor["MetallicBeechwood"] = 102] = "MetallicBeechwood";
    VehicleColor[VehicleColor["MetallicDarkBeechwood"] = 103] = "MetallicDarkBeechwood";
    VehicleColor[VehicleColor["MetallicChocoOrange"] = 104] = "MetallicChocoOrange";
    VehicleColor[VehicleColor["MetallicBeachSand"] = 105] = "MetallicBeachSand";
    VehicleColor[VehicleColor["MetallicSunBleechedSand"] = 106] = "MetallicSunBleechedSand";
    VehicleColor[VehicleColor["MetallicCream"] = 107] = "MetallicCream";
    VehicleColor[VehicleColor["UtilBrown"] = 108] = "UtilBrown";
    VehicleColor[VehicleColor["UtilMediumBrown"] = 109] = "UtilMediumBrown";
    VehicleColor[VehicleColor["UtilLightBrown"] = 110] = "UtilLightBrown";
    VehicleColor[VehicleColor["MetallicWhite"] = 111] = "MetallicWhite";
    VehicleColor[VehicleColor["MetallicFrostWhite"] = 112] = "MetallicFrostWhite";
    VehicleColor[VehicleColor["WornHoneyBeige"] = 113] = "WornHoneyBeige";
    VehicleColor[VehicleColor["WornBrown"] = 114] = "WornBrown";
    VehicleColor[VehicleColor["WornDarkBrown"] = 115] = "WornDarkBrown";
    VehicleColor[VehicleColor["WornStrawBeige"] = 116] = "WornStrawBeige";
    VehicleColor[VehicleColor["BrushedSteel"] = 117] = "BrushedSteel";
    VehicleColor[VehicleColor["BrushedBlackSteel"] = 118] = "BrushedBlackSteel";
    VehicleColor[VehicleColor["BrushedAluminium"] = 119] = "BrushedAluminium";
    VehicleColor[VehicleColor["Chrome"] = 120] = "Chrome";
    VehicleColor[VehicleColor["WornOffWhite"] = 121] = "WornOffWhite";
    VehicleColor[VehicleColor["UtilOffWhite"] = 122] = "UtilOffWhite";
    VehicleColor[VehicleColor["WornOrange"] = 123] = "WornOrange";
    VehicleColor[VehicleColor["WornLightOrange"] = 124] = "WornLightOrange";
    VehicleColor[VehicleColor["MetallicSecuricorGreen"] = 125] = "MetallicSecuricorGreen";
    VehicleColor[VehicleColor["WornTaxiYellow"] = 126] = "WornTaxiYellow";
    VehicleColor[VehicleColor["PoliceCarBlue"] = 127] = "PoliceCarBlue";
    VehicleColor[VehicleColor["MatteGreen"] = 128] = "MatteGreen";
    VehicleColor[VehicleColor["MatteBrown"] = 129] = "MatteBrown";
    VehicleColor[VehicleColor["MatteWhite"] = 131] = "MatteWhite";
    VehicleColor[VehicleColor["WornWhite"] = 132] = "WornWhite";
    VehicleColor[VehicleColor["WornOliveArmyGreen"] = 133] = "WornOliveArmyGreen";
    VehicleColor[VehicleColor["PureWhite"] = 134] = "PureWhite";
    VehicleColor[VehicleColor["HotPink"] = 135] = "HotPink";
    VehicleColor[VehicleColor["Salmonpink"] = 136] = "Salmonpink";
    VehicleColor[VehicleColor["MetallicVermillionPink"] = 137] = "MetallicVermillionPink";
    VehicleColor[VehicleColor["Orange"] = 138] = "Orange";
    VehicleColor[VehicleColor["Green"] = 139] = "Green";
    VehicleColor[VehicleColor["Blue"] = 140] = "Blue";
    VehicleColor[VehicleColor["MettalicBlackBlue"] = 141] = "MettalicBlackBlue";
    VehicleColor[VehicleColor["MetallicBlackPurple"] = 142] = "MetallicBlackPurple";
    VehicleColor[VehicleColor["MetallicBlackRed"] = 143] = "MetallicBlackRed";
    VehicleColor[VehicleColor["HunterGreen"] = 144] = "HunterGreen";
    VehicleColor[VehicleColor["MetallicPurple"] = 145] = "MetallicPurple";
    VehicleColor[VehicleColor["MetaillicVDarkBlue"] = 146] = "MetaillicVDarkBlue";
    VehicleColor[VehicleColor["ModshopBlack1"] = 147] = "ModshopBlack1";
    VehicleColor[VehicleColor["MattePurple"] = 148] = "MattePurple";
    VehicleColor[VehicleColor["MatteDarkPurple"] = 149] = "MatteDarkPurple";
    VehicleColor[VehicleColor["MetallicLavaRed"] = 150] = "MetallicLavaRed";
    VehicleColor[VehicleColor["MatteForestGreen"] = 151] = "MatteForestGreen";
    VehicleColor[VehicleColor["MatteOliveDrab"] = 152] = "MatteOliveDrab";
    VehicleColor[VehicleColor["MatteDesertBrown"] = 153] = "MatteDesertBrown";
    VehicleColor[VehicleColor["MatteDesertTan"] = 154] = "MatteDesertTan";
    VehicleColor[VehicleColor["MatteFoliageGreen"] = 155] = "MatteFoliageGreen";
    VehicleColor[VehicleColor["DefaultAlloyColor"] = 156] = "DefaultAlloyColor";
    VehicleColor[VehicleColor["EpsilonBlue"] = 157] = "EpsilonBlue";
    VehicleColor[VehicleColor["PureGold"] = 158] = "PureGold";
    VehicleColor[VehicleColor["BrushedGold"] = 159] = "BrushedGold";
})(VehicleColor || (VehicleColor = {}));
export var VehicleLandingGearState;
(function (VehicleLandingGearState) {
    VehicleLandingGearState[VehicleLandingGearState["Deployed"] = 0] = "Deployed";
    VehicleLandingGearState[VehicleLandingGearState["Closing"] = 1] = "Closing";
    VehicleLandingGearState[VehicleLandingGearState["Opening"] = 2] = "Opening";
    VehicleLandingGearState[VehicleLandingGearState["Retracted"] = 3] = "Retracted";
})(VehicleLandingGearState || (VehicleLandingGearState = {}));
export var VehicleLockStatus;
(function (VehicleLockStatus) {
    VehicleLockStatus[VehicleLockStatus["None"] = 0] = "None";
    VehicleLockStatus[VehicleLockStatus["Unlocked"] = 1] = "Unlocked";
    VehicleLockStatus[VehicleLockStatus["Locked"] = 2] = "Locked";
    VehicleLockStatus[VehicleLockStatus["LockedForPlayer"] = 3] = "LockedForPlayer";
    VehicleLockStatus[VehicleLockStatus["StickPlayerInside"] = 4] = "StickPlayerInside";
    VehicleLockStatus[VehicleLockStatus["CanBeBrokenInto"] = 7] = "CanBeBrokenInto";
    VehicleLockStatus[VehicleLockStatus["CanBeBrokenIntoPersist"] = 8] = "CanBeBrokenIntoPersist";
    VehicleLockStatus[VehicleLockStatus["CannotBeTriedToEnter"] = 10] = "CannotBeTriedToEnter";
})(VehicleLockStatus || (VehicleLockStatus = {}));
export var VehicleNeonLight;
(function (VehicleNeonLight) {
    VehicleNeonLight[VehicleNeonLight["Left"] = 0] = "Left";
    VehicleNeonLight[VehicleNeonLight["Right"] = 1] = "Right";
    VehicleNeonLight[VehicleNeonLight["Front"] = 2] = "Front";
    VehicleNeonLight[VehicleNeonLight["Back"] = 3] = "Back";
})(VehicleNeonLight || (VehicleNeonLight = {}));
export var VehicleRoofState;
(function (VehicleRoofState) {
    VehicleRoofState[VehicleRoofState["Closed"] = 0] = "Closed";
    VehicleRoofState[VehicleRoofState["Opening"] = 1] = "Opening";
    VehicleRoofState[VehicleRoofState["Opened"] = 2] = "Opened";
    VehicleRoofState[VehicleRoofState["Closing"] = 3] = "Closing";
})(VehicleRoofState || (VehicleRoofState = {}));
export var VehicleSeat;
(function (VehicleSeat) {
    VehicleSeat[VehicleSeat["None"] = -3] = "None";
    VehicleSeat[VehicleSeat["Any"] = -2] = "Any";
    VehicleSeat[VehicleSeat["Driver"] = -1] = "Driver";
    VehicleSeat[VehicleSeat["Passenger"] = 0] = "Passenger";
    VehicleSeat[VehicleSeat["LeftFront"] = -1] = "LeftFront";
    VehicleSeat[VehicleSeat["RightFront"] = 0] = "RightFront";
    VehicleSeat[VehicleSeat["LeftRear"] = 1] = "LeftRear";
    VehicleSeat[VehicleSeat["RightRear"] = 2] = "RightRear";
    VehicleSeat[VehicleSeat["ExtraSeat1"] = 3] = "ExtraSeat1";
    VehicleSeat[VehicleSeat["ExtraSeat2"] = 4] = "ExtraSeat2";
    VehicleSeat[VehicleSeat["ExtraSeat3"] = 5] = "ExtraSeat3";
    VehicleSeat[VehicleSeat["ExtraSeat4"] = 6] = "ExtraSeat4";
    VehicleSeat[VehicleSeat["ExtraSeat5"] = 7] = "ExtraSeat5";
    VehicleSeat[VehicleSeat["ExtraSeat6"] = 8] = "ExtraSeat6";
    VehicleSeat[VehicleSeat["ExtraSeat7"] = 9] = "ExtraSeat7";
    VehicleSeat[VehicleSeat["ExtraSeat8"] = 10] = "ExtraSeat8";
    VehicleSeat[VehicleSeat["ExtraSeat9"] = 11] = "ExtraSeat9";
    VehicleSeat[VehicleSeat["ExtraSeat10"] = 12] = "ExtraSeat10";
    VehicleSeat[VehicleSeat["ExtraSeat11"] = 13] = "ExtraSeat11";
    VehicleSeat[VehicleSeat["ExtraSeat12"] = 14] = "ExtraSeat12";
})(VehicleSeat || (VehicleSeat = {}));
export var VehicleWindowTint;
(function (VehicleWindowTint) {
    VehicleWindowTint[VehicleWindowTint["None"] = 0] = "None";
    VehicleWindowTint[VehicleWindowTint["PureBlack"] = 1] = "PureBlack";
    VehicleWindowTint[VehicleWindowTint["DarkSmoke"] = 2] = "DarkSmoke";
    VehicleWindowTint[VehicleWindowTint["LightSmoke"] = 3] = "LightSmoke";
    VehicleWindowTint[VehicleWindowTint["Stock"] = 4] = "Stock";
    VehicleWindowTint[VehicleWindowTint["Limo"] = 5] = "Limo";
    VehicleWindowTint[VehicleWindowTint["Green"] = 6] = "Green";
})(VehicleWindowTint || (VehicleWindowTint = {}));
export var VehicleWindowIndex;
(function (VehicleWindowIndex) {
    VehicleWindowIndex[VehicleWindowIndex["FrontRightWindow"] = 1] = "FrontRightWindow";
    VehicleWindowIndex[VehicleWindowIndex["FrontLeftWindow"] = 0] = "FrontLeftWindow";
    VehicleWindowIndex[VehicleWindowIndex["BackRightWindow"] = 3] = "BackRightWindow";
    VehicleWindowIndex[VehicleWindowIndex["BackLeftWindow"] = 2] = "BackLeftWindow";
    VehicleWindowIndex[VehicleWindowIndex["ExtraWindow1"] = 4] = "ExtraWindow1";
    VehicleWindowIndex[VehicleWindowIndex["ExtraWindow2"] = 5] = "ExtraWindow2";
    VehicleWindowIndex[VehicleWindowIndex["ExtraWindow3"] = 6] = "ExtraWindow3";
    VehicleWindowIndex[VehicleWindowIndex["ExtraWindow4"] = 7] = "ExtraWindow4";
})(VehicleWindowIndex || (VehicleWindowIndex = {}));
export var VehicleModType;
(function (VehicleModType) {
    VehicleModType[VehicleModType["Spoilers"] = 0] = "Spoilers";
    VehicleModType[VehicleModType["FrontBumper"] = 1] = "FrontBumper";
    VehicleModType[VehicleModType["RearBumper"] = 2] = "RearBumper";
    VehicleModType[VehicleModType["SideSkirt"] = 3] = "SideSkirt";
    VehicleModType[VehicleModType["Exhaust"] = 4] = "Exhaust";
    VehicleModType[VehicleModType["Frame"] = 5] = "Frame";
    VehicleModType[VehicleModType["Grille"] = 6] = "Grille";
    VehicleModType[VehicleModType["Hood"] = 7] = "Hood";
    VehicleModType[VehicleModType["Fender"] = 8] = "Fender";
    VehicleModType[VehicleModType["RightFender"] = 9] = "RightFender";
    VehicleModType[VehicleModType["Roof"] = 10] = "Roof";
    VehicleModType[VehicleModType["Engine"] = 11] = "Engine";
    VehicleModType[VehicleModType["Brakes"] = 12] = "Brakes";
    VehicleModType[VehicleModType["Transmission"] = 13] = "Transmission";
    VehicleModType[VehicleModType["Horns"] = 14] = "Horns";
    VehicleModType[VehicleModType["Suspension"] = 15] = "Suspension";
    VehicleModType[VehicleModType["Armor"] = 16] = "Armor";
    VehicleModType[VehicleModType["FrontWheel"] = 23] = "FrontWheel";
    VehicleModType[VehicleModType["RearWheel"] = 24] = "RearWheel";
    VehicleModType[VehicleModType["PlateHolder"] = 25] = "PlateHolder";
    VehicleModType[VehicleModType["VanityPlates"] = 26] = "VanityPlates";
    VehicleModType[VehicleModType["TrimDesign"] = 27] = "TrimDesign";
    VehicleModType[VehicleModType["Ornaments"] = 28] = "Ornaments";
    VehicleModType[VehicleModType["Dashboard"] = 29] = "Dashboard";
    VehicleModType[VehicleModType["DialDesign"] = 30] = "DialDesign";
    VehicleModType[VehicleModType["DoorSpeakers"] = 31] = "DoorSpeakers";
    VehicleModType[VehicleModType["Seats"] = 32] = "Seats";
    VehicleModType[VehicleModType["SteeringWheels"] = 33] = "SteeringWheels";
    VehicleModType[VehicleModType["ColumnShifterLevers"] = 34] = "ColumnShifterLevers";
    VehicleModType[VehicleModType["Plaques"] = 35] = "Plaques";
    VehicleModType[VehicleModType["Speakers"] = 36] = "Speakers";
    VehicleModType[VehicleModType["Trunk"] = 37] = "Trunk";
    VehicleModType[VehicleModType["Hydraulics"] = 38] = "Hydraulics";
    VehicleModType[VehicleModType["EngineBlock"] = 39] = "EngineBlock";
    VehicleModType[VehicleModType["AirFilter"] = 40] = "AirFilter";
    VehicleModType[VehicleModType["Struts"] = 41] = "Struts";
    VehicleModType[VehicleModType["ArchCover"] = 42] = "ArchCover";
    VehicleModType[VehicleModType["Aerials"] = 43] = "Aerials";
    VehicleModType[VehicleModType["Trim"] = 44] = "Trim";
    VehicleModType[VehicleModType["Tank"] = 45] = "Tank";
    VehicleModType[VehicleModType["Windows"] = 46] = "Windows";
    VehicleModType[VehicleModType["Livery"] = 48] = "Livery";
})(VehicleModType || (VehicleModType = {}));
export var VehicleToggleModType;
(function (VehicleToggleModType) {
    VehicleToggleModType[VehicleToggleModType["Turbo"] = 18] = "Turbo";
    VehicleToggleModType[VehicleToggleModType["TireSmoke"] = 20] = "TireSmoke";
    VehicleToggleModType[VehicleToggleModType["XenonHeadlights"] = 22] = "XenonHeadlights";
})(VehicleToggleModType || (VehicleToggleModType = {}));
export var VehiclePaintType;
(function (VehiclePaintType) {
    VehiclePaintType[VehiclePaintType["Normal"] = 0] = "Normal";
    VehiclePaintType[VehiclePaintType["Metallic"] = 1] = "Metallic";
    VehiclePaintType[VehiclePaintType["Pearl"] = 2] = "Pearl";
    VehiclePaintType[VehiclePaintType["Matte"] = 3] = "Matte";
    VehiclePaintType[VehiclePaintType["Metal"] = 4] = "Metal";
    VehiclePaintType[VehiclePaintType["Chrome"] = 5] = "Chrome";
})(VehiclePaintType || (VehiclePaintType = {}));
export var VehicleDoorIndex;
(function (VehicleDoorIndex) {
    VehicleDoorIndex[VehicleDoorIndex["FrontRightDoor"] = 1] = "FrontRightDoor";
    VehicleDoorIndex[VehicleDoorIndex["FrontLeftDoor"] = 0] = "FrontLeftDoor";
    VehicleDoorIndex[VehicleDoorIndex["BackRightDoor"] = 3] = "BackRightDoor";
    VehicleDoorIndex[VehicleDoorIndex["BackLeftDoor"] = 2] = "BackLeftDoor";
    VehicleDoorIndex[VehicleDoorIndex["Hood"] = 4] = "Hood";
    VehicleDoorIndex[VehicleDoorIndex["Trunk"] = 5] = "Trunk";
})(VehicleDoorIndex || (VehicleDoorIndex = {}));
export var VehicleWheelType;
(function (VehicleWheelType) {
    VehicleWheelType[VehicleWheelType["Sport"] = 0] = "Sport";
    VehicleWheelType[VehicleWheelType["Muscle"] = 1] = "Muscle";
    VehicleWheelType[VehicleWheelType["Lowrider"] = 2] = "Lowrider";
    VehicleWheelType[VehicleWheelType["SUV"] = 3] = "SUV";
    VehicleWheelType[VehicleWheelType["Offroad"] = 4] = "Offroad";
    VehicleWheelType[VehicleWheelType["Tuner"] = 5] = "Tuner";
    VehicleWheelType[VehicleWheelType["BikeWheels"] = 6] = "BikeWheels";
    VehicleWheelType[VehicleWheelType["HighEnd"] = 7] = "HighEnd";
    VehicleWheelType[VehicleWheelType["BennysOriginals"] = 8] = "BennysOriginals";
    VehicleWheelType[VehicleWheelType["BennysBespoke"] = 9] = "BennysBespoke";
})(VehicleWheelType || (VehicleWheelType = {}));
export var VehicleWheelIndex;
(function (VehicleWheelIndex) {
    VehicleWheelIndex[VehicleWheelIndex["FrontLeftWheel"] = 0] = "FrontLeftWheel";
    VehicleWheelIndex[VehicleWheelIndex["FrontRightWheel"] = 1] = "FrontRightWheel";
    VehicleWheelIndex[VehicleWheelIndex["MidLeftWheel"] = 2] = "MidLeftWheel";
    VehicleWheelIndex[VehicleWheelIndex["MidRightWheel"] = 3] = "MidRightWheel";
    VehicleWheelIndex[VehicleWheelIndex["RearLeftWheel"] = 4] = "RearLeftWheel";
    VehicleWheelIndex[VehicleWheelIndex["RearRightWheel"] = 5] = "RearRightWheel";
    VehicleWheelIndex[VehicleWheelIndex["TrailerMidLeftWheel"] = 45] = "TrailerMidLeftWheel";
    VehicleWheelIndex[VehicleWheelIndex["TrailerMidRightWheel"] = 47] = "TrailerMidRightWheel";
})(VehicleWheelIndex || (VehicleWheelIndex = {}));
