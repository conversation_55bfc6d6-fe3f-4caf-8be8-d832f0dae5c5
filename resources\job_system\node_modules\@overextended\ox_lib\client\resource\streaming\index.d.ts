export declare const requestAnimDict: (animDict: string, timeout?: number) => string | Promise<string | undefined>;
export declare const requestAnimSet: (animSet: string, timeout?: number) => string | Promise<string | undefined>;
export declare const requestModel: (model: string | number, timeout?: number) => number | Promise<number | undefined>;
export declare const requestNamedPtfxAsset: (ptFxName: string, timeout?: number) => string | Promise<string | undefined>;
export declare const requestScaleformMovie: (scaleformName: string, timeout?: number) => string | Promise<string | undefined>;
export declare const requestStreamedTextureDict: (textureDict: string, timeout?: number) => string | Promise<string | undefined>;
export declare const requestWeaponAsset: (weaponHash: string | number, timeout?: number, weaponResourceFlags?: number, extraWeaponComponentFlags?: number) => string | number | Promise<string | number | undefined>;
